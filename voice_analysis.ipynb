{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "#voicemdf"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["<bound method IndexOpsMixin.tolist of Index(['OrdersCnt', 'PicksCnt', 'KYDT', 'Blked_AXSessionID',\n", "       'Blked_SQLSessionID', 'Blked_Lines', 'Blked_AXUser', 'Blked_Host',\n", "       'Max_TimeInSec', 'Blked_WaitType', 'Blked_<PERSON>', 'Blked_SQL',\n", "       'Blked_Status', 'Blked_Command', 'Blking_AXSessionID',\n", "       'Blking_SQLSessionID', 'Blking_AXUser', 'Blking_Host',\n", "       'Blking_WaitType', 'Blking_<PERSON>', 'Blking_SQL', 'Blking_Status',\n", "       'Blking_Command'],\n", "      dtype='object')>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["voicemdf = pd.read_csv(\"VoiceBlkMon.csv\")\n", "voicemdf.columns.tolist"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Rows: 938, Columns: 23\n"]}], "source": ["shape = voicemdf.shape\n", "print(f\"Rows: {shape[0]:,}, Columns: {shape[1]}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 938 entries, 0 to 937\n", "Data columns (total 23 columns):\n", " #   Column               Non-Null Count  Dtype  \n", "---  ------               --------------  -----  \n", " 0   OrdersCnt            938 non-null    int64  \n", " 1   PicksCnt             938 non-null    int64  \n", " 2   KYDT                 938 non-null    object \n", " 3   Blked_AXSessionID    879 non-null    float64\n", " 4   Blked_SQLSessionID   936 non-null    float64\n", " 5   Blked_Lines          936 non-null    float64\n", " 6   Blked_AXUser         879 non-null    object \n", " 7   Blked_Host           936 non-null    object \n", " 8   Max_TimeInSec        936 non-null    float64\n", " 9   Blked_WaitType       936 non-null    object \n", " 10  Blked_DB             936 non-null    object \n", " 11  Blked_SQL            936 non-null    object \n", " 12  Blked_Status         936 non-null    object \n", " 13  Blked_Command        936 non-null    object \n", " 14  Blking_AXSessionID   827 non-null    float64\n", " 15  Blking_SQLSessionID  936 non-null    float64\n", " 16  Blking_AXUser        827 non-null    object \n", " 17  Blking_Host          936 non-null    object \n", " 18  Blking_WaitType      202 non-null    object \n", " 19  Blking_DB            936 non-null    object \n", " 20  Blking_SQL           936 non-null    object \n", " 21  Blking_Status        936 non-null    object \n", " 22  Blking_Command       936 non-null    object \n", "dtypes: float64(6), int64(2), object(15)\n", "memory usage: 168.7+ KB\n"]}], "source": ["voicemdf.info()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 938 entries, 0 to 937\n", "Data columns (total 23 columns):\n", " #   Column               Non-Null Count  Dtype \n", "---  ------               --------------  ----- \n", " 0   OrdersCnt            938 non-null    int64 \n", " 1   PicksCnt             938 non-null    int64 \n", " 2   KYDT                 938 non-null    object\n", " 3   Blked_AXSessionID    879 non-null    Int64 \n", " 4   Blked_SQLSessionID   936 non-null    Int64 \n", " 5   Blked_Lines          936 non-null    Int64 \n", " 6   Blked_AXUser         879 non-null    object\n", " 7   Blked_Host           936 non-null    object\n", " 8   Max_TimeInSec        936 non-null    Int64 \n", " 9   Blked_WaitType       936 non-null    object\n", " 10  Blked_DB             936 non-null    object\n", " 11  Blked_SQL            936 non-null    object\n", " 12  Blked_Status         936 non-null    object\n", " 13  Blked_Command        936 non-null    object\n", " 14  Blking_AXSessionID   827 non-null    Int64 \n", " 15  Blking_SQLSessionID  936 non-null    Int64 \n", " 16  Blking_AXUser        827 non-null    object\n", " 17  Blking_Host          936 non-null    object\n", " 18  Blking_WaitType      202 non-null    object\n", " 19  Blking_DB            936 non-null    object\n", " 20  Blking_SQL           936 non-null    object\n", " 21  Blking_Status        936 non-null    object\n", " 22  Blking_Command       936 non-null    object\n", "dtypes: Int64(6), int64(2), object(15)\n", "memory usage: 174.2+ KB\n"]}], "source": ["voicemdf = voicemdf.astype({'Blked_AXSessionID': 'Int64', 'Blked_SQLSessionID': 'Int64', 'Blked_Lines': 'Int64','Blking_AXSessionID':'Int64',\n", "                            'Blking_SQLSessionID':'Int64', 'Max_TimeInSec': 'Int64'})\n", "voicemdf.info()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["Blked_SQL\n", "(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 nvarchar(5),@P5 bigint)SELECT T1.ITEMID,T1.INVENTDIMID,T1.RECID FROM INVENTSUM T1 WITH ( UPDLOCK) CROSS JOIN INVENTSUMDELTADIM T2 WHERE ((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (((T2.PARTITION=@P3) AND (T2.DATAAREAID=@P4)) AND (((T2.ITEMID=T1.ITEMID) AND (T2.INVENTDIMID=T1.INVENTDIMID)) AND (T2.TTSID=@P5)))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       717\n", "(@p1 NVARCHAR(4), @p2 NVARCHAR(4), @p3 BIGINT, @p4 BIGINT)UPDATE INVENTSUM SET INVENTSUM.POSTEDQTY = INVENTSUM.POSTEDQTY + INVENTSUMDELTA.SUM_POSTEDQTY, INVENTSUM.POSTEDVALUE = INVENTSUM.POSTEDVALUE + INVENTSUMDELTA.SUM_POSTEDVALUE, INVENTSUM.DEDUCTED = INVENTSUM.DEDUCTED + INVENTSUMDELTA.SUM_DEDUCTED, INVENTSUM.RECEIVED = INVENTSUM.RECEIVED + INVENTSUMDELTA.SUM_RECEIVED, INVENTSUM.RESERVORDERED = INVENTSUM.RESERVORDERED + INVENTSUMDELTA.SUM_RESERVORDERED, INVENTSUM.RESERVPHYSICAL = INVENTSUM.RESERVPHYSICAL + INVENTSUMDELTA.SUM_RESERVPHYSICAL, INVENTSUM.ONORDER = INVENTSUM.ONORDER + INVENTSUMDELTA.SUM_ONORDER, INVENTSUM.ORDERED = INVENTSUM.ORDERED + INVENTSUMDELTA.SUM_ORDERED, INVENTSUM.QUOTATIONISSUE = INVENTSUM.QUOTATIONISSUE + INVENTSUMDELTA.SUM_QUOTATIONISSUE, INVENTSUM.QUOTATIONRECEIPT = INVENTSUM.QUOTATIONRECEIPT + INVENTSUMDELTA.SUM_QUOTATIONRECEIPT, INVENTSUM.REGISTERED = INVENTSUM.REGISTERED + INVENTSUMDELTA.SUM_REGISTERED, INVENTSUM.PICKED = INVENTSUM.PICKED + INVENTSUMDELTA.SUM_PICKED, INVENTSUM.AVAILORDERED = INVENTSUM.AVAILORDERED + INVENTSUMDELTA.SUM_AVAILORDERED, INVENTSUM.AVAILPHYSICAL = INVENTSUM.AVAILPHYSICAL + INVENTSUMDELTA.SUM_AVAILPHYSICAL, INVENTSUM.PHYSICALVALUE = INVENTSUM.PHYSICALVALUE + INVENTSUMDELTA.SUM_PHYSICALVALUE, INVENTSUM.ARRIVED = INVENTSUM.ARRIVED + INVENTSUMDELTA.SUM_ARRIVED, INVENTSUM.PHYSICALINVENT = INVENTSUM.PHYSICALINVENT + INVENTSUMDELTA.SUM_PHYSICALINVENT, INVENTSUM.PHYSICALVALUESECCUR_RU = INVENTSUM.PHYSICALVALUESECCUR_RU + INVENTSUMDELTA.SUM_PHYSICALVALUESECCUR_RU, INVENTSUM.POSTEDVALUESECCUR_RU = INVENTSUM.POSTEDVALUESECCUR_RU + INVENTSUMDELTA.SUM_POSTEDVALUESECCUR_RU, INVENTSUM.LASTUPDDATEPHYSICAL = CASE WHEN INVENTSUM.LASTUPDDATEPHYSICAL > INVENTSUMDELTA.MAX_LASTUPDDATEPHYSICAL THEN INVENTSUM.LASTUPDDATEPHYSICAL ELSE INVENTSUMDELTA.MAX_LASTUPDDATEPHYSICAL END , INVENTSUM.LASTUPDDATEEXPECTED = CASE WHEN INVENTSUM.LASTUPDDATEEXPECTED > INVENTSUMDELTA.MAX_LASTUPDDATEEXPECTED THEN INVENTSUM.LASTUPDDATEEXPECTED ELSE INVENTSUMDELTA.MAX_LASTUPDDATEEXPECTED END , INVENTSUM.MODIFIEDDATETIME = dateadd(ms, -datepart(ms,getutcdate()), getutcdate()) , INVENTSUM.CLOSEDQTY = CASE WHEN ((INVENTSUM.POSTEDQTY + INVENTSUMDELTA.SUM_POSTEDQTY) = 0 AND (INVENTSUM.DEDUCTED + INVENTSUMDELTA.SUM_DEDUCTED) = 0 AND (INVENTSUM.PICKED + INVENTSUMDELTA.SUM_PICKED) = 0 AND (INVENTSUM.RECEIVED + INVENTSUMDELTA.SUM_RECEIVED) = 0 AND (INVENTSUM.REGISTERED + INVENTSUMDELTA.SUM_REGISTERED) = 0 AND (INVENTSUM.RESERVPHYSICAL + INVENTSUMDELTA.SUM_RESERVPHYSICAL) = 0 AND (INVENTSUM.RESERVORDERED + INVENTSUMDELTA.SUM_RESERVORDERED) = 0 AND (INVENTSUM.ONORDER + INVENTSUMDELTA.SUM_ONORDER) = 0 AND (INVENTSUM.ORDERED + INVENTSUMDELTA.SUM_ORDERED) = 0 AND (INVENTSUM.ARRIVED + INVENTSUMDELTA.SUM_ARRIVED) = 0 AND (INVENTSUM.QUOTATIONISSUE + INVENTSUMDELTA.SUM_QUOTATIONISSUE) = 0 AND (INVENTSUM.QUOTATIONRECEIPT + INVENTSUMDELTA.SUM_QUOTATIONRECEIPT) = 0) THEN 1 ELSE 0 END,INVENTSUM.CLOSED = CASE WHEN ((INVENTSUM.POSTEDQTY + INVENTSUMDELTA.SUM_POSTEDQTY) = 0 AND (INVENTSUM.DEDUCTED + INVENTSUMDELTA.SUM_DEDUCTED) = 0 AND (INVENTSUM.PICKED + INVENTSUMDELTA.SUM_PICKED) = 0 AND (INVENTSUM.RECEIVED + INVENTSUMDELTA.SUM_RECEIVED) = 0 AND (INVENTSUM.REGISTERED + INVENTSUMDELTA.SUM_REGISTERED) = 0 AND (INVENTSUM.RESERVPHYSICAL + INVENTSUMDELTA.SUM_RESERVPHYSICAL) = 0 AND (INVENTSUM.RESERVORDERED + INVENTSUMDELTA.SUM_RESERVORDERED) = 0 AND (INVENTSUM.ONORDER + INVENTSUMDELTA.SUM_ONORDER) = 0 AND (INVENTSUM.ORDERED + INVENTSUMDELTA.SUM_ORDERED) = 0 AND (INVENTSUM.ARRIVED + INVENTSUMDELTA.SUM_ARRIVED) = 0 AND (INVENTSUM.QUOTATIONISSUE + INVENTSUMDELTA.SUM_QUOTATIONISSUE) = 0 AND (INVENTSUM.QUOTATIONRECEIPT + INVENTSUMDELTA.SUM_QUOTATIONRECEIPT) = 0 AND (INVENTSUM.POSTEDVALUE + INVENTSUMDELTA.SUM_POSTEDVALUE) = 0 AND (INVENTSUM.PHYSICALVALUE + INVENTSUMDELTA.SUM_PHYSICALVALUE) = 0) THEN 1 ELSE 0 END FROM (SELECT SUM(POSTEDQTY) AS SUM_POSTEDQTY, SUM(POSTEDVALUE) AS SUM_POSTEDVALUE, SUM(DEDUCTED) AS SUM_DEDUCTED, SUM(RECEIVED) AS SUM_RECEIVED, SUM(RESERVORDERED) AS SUM_RESERVORDERED, SUM(RESERVPHYSICAL) AS SUM_RESERVPHYSICAL, SUM(ONORDER) AS SUM_ONORDER, SUM(ORDERED) AS SUM_ORDERED, SUM(QUOTATIONISSUE) AS SUM_QUOTATIONISSUE, SUM(QUOTATIONRECEIPT) AS SUM_QUOTATIONRECEIPT, SUM(REGISTERED) AS SUM_REGISTERED, SUM(PICKED) AS SUM_PICKED, SUM(AVAILORDERED) AS SUM_AVAILORDERED, SUM(AVAILPHYSICAL) AS SUM_AVAILPHYSICAL, SUM(PHYSICALVALUE) AS SUM_PHYSICALVALUE, SUM(ARRIVED) AS SUM_ARRIVED, SUM(PHYSICALINVENT) AS SUM_PHYSICALINVENT, SUM(POSTEDVALUESECCUR_RU) AS SUM_POSTEDVALUESECCUR_RU, SUM(PHYSICALVALUESECCUR_RU) AS SUM_PHYSICALVALUESECCUR_RU, MAX(LASTUPDDATEPHYSICAL) AS MAX_LASTUPDDATEPHYSICAL, MAX(LASTUPDDATEEXPECTED) AS MAX_LASTUPDDATEEXPECTED, INVENTSUMDELTA.ITEMID, INVENTSUMDELTA.INVENTDIMID FROM INVENTSUMDELTA WHERE INVENTSUMDELTA.DATAAREAID = @p2 AND INVENTSUMDELTA.PARTITION = @p4 AND INVENTSUMDELTA.TTSID = @p3 AND INVENTSUMDELTA.ISAGGREGATED = 0 GROUP BY INVENTSUMDELTA.ITEMID, INVENTSUMDELTA.INVENTDIMID) AS INVENTSUMDELTA WHERE INVENTSUM.DATAAREAID = @p1 AND INVENTSUM.PARTITION = @p4 AND INVENTSUM.ITEMID = INVENTSUMDELTA.ITEMID AND INVENTSUM.INVENTDIMID = INVENTSUMDELTA.INVENTDIMID     54\n", "(@P1 numeric(32,16),@P2 numeric(32,16),@P3 int,@P4 bigint,@P5 int)UPDATE WHSLOCATIONLOAD SET AVAILABLEVOLUME=@P1,CURRENTVOLUME=@P2,RECVERSION=@P3 WHERE ((RECID=@P4) AND (RECVERSION=@P5))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     52\n", "(@P1 int,@P2 datetime2,@P3 int,@P4 nvarchar(11),@P5 int,@P6 datetime2,@P7 nvarchar(9),@P8 bigint,@P9 int)UPDATE WHSCONTAINERTABLE SET CONTAINERSTATUS=@P1,CLOSECONTAINERUTCDATETIME=@P2,CLOSECON<PERSON>INERUTCDATETIMETZID=@P3,HACLOSEDBYUSERID=@P4,RECVERSION=@P5,MODIFIEDDATETIME=@P6,MODIFIEDBY=@P7 WHERE ((RECID=@P8) AND (RECVERSION=@P9))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     32\n", "(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 int)DELETE FROM TWEJURLINERESULT WHERE (((PARTITION=@P1) AND (DATAAREAID=@P2)) AND ((ORIGREFRECID=@P3) AND (ORIGREFTABLEID=@P4)))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   23\n", "Name: count, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["top_5_blocked_queries = voicemdf['Blked_SQL'].value_counts().head(5)\n", "top_5_blocked_queries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Most blocked query:\n", "(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 nvarchar(5),@P5 bigint)\n", "SELECT \n", "    T1.ITEMID,T1.INVENTDIMID,T1.RECID \n", "FROM \n", "    INVENTSUM T1 WITH ( UPDLOCK) CROSS JOIN INVENTSUMDELTADIM T2 \n", "WHERE \n", "    ((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (((T2.PARTITION=@P3) AND (T2.DATAAREAID=@P4)) \n", "    AND (((T2.ITEMID=T1.ITEMID) AND (T2.INVENTDIMID=T1.INVENTDIMID)) AND (T2.TTSID=@P5)))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# Count the occurrences of each unique SQL query in the 'Blking_SQL' column\n", "query_counts = voicemdf['Blking_SQL'].value_counts()\n", "\n", "# Total rows before filtering\n", "total_rows = len(voicemdf)\n", "\n", "# Filter queries occurring more than five times\n", "filtered_queries = query_counts[query_counts > 5]\n", "\n", "# Plot a bar chart\n", "plt.bar(range(len(filtered_queries)), filtered_queries.values, align='center')\n", "plt.xlabel('Query')\n", "plt.ylabel('Total Occurrences')\n", "plt.title('Top Queries Involved in Blocking (Occurrences > 5)')\n", "\n", "# Annotate with exact numbers on top of each bar\n", "#for i, count in enumerate(filtered_queries.values):\n", " #   plt.text(i, count + 0.02 * total_rows, f\"{count}/{total_rows}\", ha='center', va='bottom', rotation=45)\n", "\n", "plt.xticks(range(len(filtered_queries)), ['Query{}'.format(i+1) for i in range(len(filtered_queries))])\n", "\n", "# Adjust legend position\n", "plt.legend(['Occurrences'], bbox_to_anchor=(0.5, -0.15), loc='upper center', borderaxespad=0.)\n", "\n", "# Save the figure with adjusted layout\n", "plt.tight_layout()\n", "plt.savefig('bar_chart.png', bbox_inches='tight')\n", "\n", "# Display the plot\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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***************************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", "text/plain": ["<Figure size 1200x900 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# Count the occurrences of each unique SQL query in the 'Blking_SQL' column\n", "query_counts = voicemdf['Blking_SQL'].value_counts()\n", "\n", "# Total rows before filtering\n", "total_rows = len(voicemdf)\n", "\n", "# Filter queries occurring more than five times\n", "filtered_queries = query_counts[query_counts > 5]\n", "\n", "# Create subplots with extra height for the legend\n", "fig, ax = plt.subplots(figsize=(12, 9))\n", "plt.subplots_adjust(bottom=0.2)\n", "\n", "# Plot a bar chart\n", "ax.bar(range(len(filtered_queries)), filtered_queries.values, align='center')\n", "ax.set_xlabel('Query')\n", "ax.set_ylabel('Total Occurrences')\n", "ax.set_title('Top Queries Involved in Blocking (Occurrences > 5)')\n", "\n", "# Annotate with exact numbers on top of each bar\n", "for i, count in enumerate(filtered_queries.values):\n", "    ax.text(i, count + 0.02 * total_rows, f\"{count}/{total_rows}\", ha='center', va='bottom', rotation=0)\n", "\n", "ax.set_xticks(range(len(filtered_queries)))\n", "ax.set_xticklabels(['Query{}'.format(i+1) for i in range(len(filtered_queries))])\n", "\n", "# Add legend outside the plot\n", "ax.legend(['Occurrences'], bbox_to_anchor=(1.05, 1), loc='upper left')\n", "\n", "# Save the figure with adjusted layout\n", "plt.tight_layout()\n", "plt.savefig('bar_chart.png', bbox_inches='tight')\n", "\n", "# Display the plot\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["Blking_SQL\n", "(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 nvarchar(5),@P5 int,@P6 bigint,@P7 nvarchar(5),@P8 bigint,@P9 int,@P10 int,@P11 int,@P12 int,@P13 int,@P14 int,@P15 int,@P16 int,@P17 int)SELECT T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU,SUM(T2.POSTEDQTY),<PERSON>UM(T2.POSTEDVALUE),<PERSON>UM(T2.PHYSICALVALUE),<PERSON><PERSON>(T2.DEDUCTED),<PERSON>UM(T2.RECEIVED),<PERSON>UM(T2.RESERVPHYSICAL),SUM(T2.RESERVORDERED),SUM(T2.REGISTERED),SUM(T2.PICKED),SUM(T2.ONORDER),SUM(T2.ORDERED),SUM(T2.ARRIVED),SUM(T2.QUOTATIONRECEIPT),SUM(T2.QUOTATIONISSUE),SUM(T2.AVAILPHYSICAL),SUM(T2.AVAILORDERED),SUM(T2.PHYSICALINVENT),SUM(T2.POSTEDVALUESECCUR_RU),SUM(T2.PHYSICALVALUESECCUR_RU) FROM INVENTSUMDELTADIM T1 CROSS JOIN INVENTSUM T2 CROSS JOIN INVENTDIM T3 WHERE ((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (((T2.PARTITION=@P3) AND (T2.DATAAREAID=@P4)) AND ((T2.ITEMID=T1.ITEMID) AND (T2.CLOSED=@P5))) AND (((T3.PARTITION=@P6) AND (T3.DATAAREAID=@P7)) AND (((((((((((T2.INVENTDIMID=T3.INVENTDIMID) AND (T1.TTSID=@P8)) AND (T1.AREALLACTIVEDIMENSIONSSPECIFIED=@P9)) AND (T1.CHECKTYPE<@P10)) AND ((T1.INVENTSIZEID=T3.INVENTSIZEID) OR (T1.INVENTSIZEIDFLAG=@P11))) AND ((T1.INVENTCOLORID=T3.INVENTCOLORID) OR (T1.INVENTCOLORIDFLAG=@P12))) AND ((T1.INVENTSITEID=T3.INVENTSITEID) OR (T1.INVENTSITEIDFLAG=@P13))) AND ((T1.INVENTLOCATIONID=T3.INVENTLOCATIONID) OR (T1.INVENTLOCATIONIDFLAG=@P14))) AND ((T1.LICENSEPLATEID=T3.LICENSEPLATEID) OR (T1.LICENSEPLATEFLAG=@P15))) AND ((T1.INVENTSTATUSID=T3.INVENTSTATUSID) OR (T1.INVENTSTATUSFLAG=@P16))) AND ((T1.INVENTSERIALID=T3.INVENTSERIALID) OR (T1.INVENTSERIALIDFLAG=@P17)))) GROUP BY T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU ORDER BY T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU OPTION(FORCE ORDER)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 698\n", "(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 nvarchar(5),@P5 bigint)SELECT T1.ITEMID,T1.INVENTDIMID,T1.RECID FROM INVENTSUM T1 WITH ( UPDLOCK) CROSS JOIN INVENTSUMDELTADIM T2 WHERE ((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (((T2.PARTITION=@P3) AND (T2.DATAAREAID=@P4)) AND (((T2.ITEMID=T1.ITEMID) AND (T2.INVENTDIMID=T1.INVENTDIMID)) AND (T2.TTSID=@P5)))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       166\n", "(@p1 NVARCHAR(4), @p2 NVARCHAR(4), @p3 BIGINT, @p4 BIGINT)UPDATE INVENTSUM SET INVENTSUM.POSTEDQTY = INVENTSUM.POSTEDQTY + INVENTSUMDELTA.SUM_POSTEDQTY, INVENTSUM.POSTEDVALUE = INVENTSUM.POSTEDVALUE + INVENTSUMDELTA.SUM_POSTEDVALUE, INVENTSUM.DEDUCTED = INVENTSUM.DEDUCTED + INVENTSUMDELTA.SUM_DEDUCTED, INVENTSUM.RECEIVED = INVENTSUM.RECEIVED + INVENTSUMDELTA.SUM_RECEIVED, INVENTSUM.RESERVORDERED = INVENTSUM.RESERVORDERED + INVENTSUMDELTA.SUM_RESERVORDERED, INVENTSUM.RESERVPHYSICAL = INVENTSUM.RESERVPHYSICAL + INVENTSUMDELTA.SUM_RESERVPHYSICAL, INVENTSUM.ONORDER = INVENTSUM.ONORDER + INVENTSUMDELTA.SUM_ONORDER, INVENTSUM.ORDERED = INVENTSUM.ORDERED + INVENTSUMDELTA.SUM_ORDERED, INVENTSUM.QUOTATIONISSUE = INVENTSUM.QUOTATIONISSUE + INVENTSUMDELTA.SUM_QUOTATIONISSUE, INVENTSUM.QUOTATIONRECEIPT = INVENTSUM.QUOTATIONRECEIPT + INVENTSUMDELTA.SUM_QUOTATIONRECEIPT, INVENTSUM.REGISTERED = INVENTSUM.REGISTERED + INVENTSUMDELTA.SUM_REGISTERED, INVENTSUM.PICKED = INVENTSUM.PICKED + INVENTSUMDELTA.SUM_PICKED, INVENTSUM.AVAILORDERED = INVENTSUM.AVAILORDERED + INVENTSUMDELTA.SUM_AVAILORDERED, INVENTSUM.AVAILPHYSICAL = INVENTSUM.AVAILPHYSICAL + INVENTSUMDELTA.SUM_AVAILPHYSICAL, INVENTSUM.PHYSICALVALUE = INVENTSUM.PHYSICALVALUE + INVENTSUMDELTA.SUM_PHYSICALVALUE, INVENTSUM.ARRIVED = INVENTSUM.ARRIVED + INVENTSUMDELTA.SUM_ARRIVED, INVENTSUM.PHYSICALINVENT = INVENTSUM.PHYSICALINVENT + INVENTSUMDELTA.SUM_PHYSICALINVENT, INVENTSUM.PHYSICALVALUESECCUR_RU = INVENTSUM.PHYSICALVALUESECCUR_RU + INVENTSUMDELTA.SUM_PHYSICALVALUESECCUR_RU, INVENTSUM.POSTEDVALUESECCUR_RU = INVENTSUM.POSTEDVALUESECCUR_RU + INVENTSUMDELTA.SUM_POSTEDVALUESECCUR_RU, INVENTSUM.LASTUPDDATEPHYSICAL = CASE WHEN INVENTSUM.LASTUPDDATEPHYSICAL > INVENTSUMDELTA.MAX_LASTUPDDATEPHYSICAL THEN INVENTSUM.LASTUPDDATEPHYSICAL ELSE INVENTSUMDELTA.MAX_LASTUPDDATEPHYSICAL END , INVENTSUM.LASTUPDDATEEXPECTED = CASE WHEN INVENTSUM.LASTUPDDATEEXPECTED > INVENTSUMDELTA.MAX_LASTUPDDATEEXPECTED THEN INVENTSUM.LASTUPDDATEEXPECTED ELSE INVENTSUMDELTA.MAX_LASTUPDDATEEXPECTED END , INVENTSUM.MODIFIEDDATETIME = dateadd(ms, -datepart(ms,getutcdate()), getutcdate()) , INVENTSUM.CLOSEDQTY = CASE WHEN ((INVENTSUM.POSTEDQTY + INVENTSUMDELTA.SUM_POSTEDQTY) = 0 AND (INVENTSUM.DEDUCTED + INVENTSUMDELTA.SUM_DEDUCTED) = 0 AND (INVENTSUM.PICKED + INVENTSUMDELTA.SUM_PICKED) = 0 AND (INVENTSUM.RECEIVED + INVENTSUMDELTA.SUM_RECEIVED) = 0 AND (INVENTSUM.REGISTERED + INVENTSUMDELTA.SUM_REGISTERED) = 0 AND (INVENTSUM.RESERVPHYSICAL + INVENTSUMDELTA.SUM_RESERVPHYSICAL) = 0 AND (INVENTSUM.RESERVORDERED + INVENTSUMDELTA.SUM_RESERVORDERED) = 0 AND (INVENTSUM.ONORDER + INVENTSUMDELTA.SUM_ONORDER) = 0 AND (INVENTSUM.ORDERED + INVENTSUMDELTA.SUM_ORDERED) = 0 AND (INVENTSUM.ARRIVED + INVENTSUMDELTA.SUM_ARRIVED) = 0 AND (INVENTSUM.QUOTATIONISSUE + INVENTSUMDELTA.SUM_QUOTATIONISSUE) = 0 AND (INVENTSUM.QUOTATIONRECEIPT + INVENTSUMDELTA.SUM_QUOTATIONRECEIPT) = 0) THEN 1 ELSE 0 END,INVENTSUM.CLOSED = CASE WHEN ((INVENTSUM.POSTEDQTY + INVENTSUMDELTA.SUM_POSTEDQTY) = 0 AND (INVENTSUM.DEDUCTED + INVENTSUMDELTA.SUM_DEDUCTED) = 0 AND (INVENTSUM.PICKED + INVENTSUMDELTA.SUM_PICKED) = 0 AND (INVENTSUM.RECEIVED + INVENTSUMDELTA.SUM_RECEIVED) = 0 AND (INVENTSUM.REGISTERED + INVENTSUMDELTA.SUM_REGISTERED) = 0 AND (INVENTSUM.RESERVPHYSICAL + INVENTSUMDELTA.SUM_RESERVPHYSICAL) = 0 AND (INVENTSUM.RESERVORDERED + INVENTSUMDELTA.SUM_RESERVORDERED) = 0 AND (INVENTSUM.ONORDER + INVENTSUMDELTA.SUM_ONORDER) = 0 AND (INVENTSUM.ORDERED + INVENTSUMDELTA.SUM_ORDERED) = 0 AND (INVENTSUM.ARRIVED + INVENTSUMDELTA.SUM_ARRIVED) = 0 AND (INVENTSUM.QUOTATIONISSUE + INVENTSUMDELTA.SUM_QUOTATIONISSUE) = 0 AND (INVENTSUM.QUOTATIONRECEIPT + INVENTSUMDELTA.SUM_QUOTATIONRECEIPT) = 0 AND (INVENTSUM.POSTEDVALUE + INVENTSUMDELTA.SUM_POSTEDVALUE) = 0 AND (INVENTSUM.PHYSICALVALUE + INVENTSUMDELTA.SUM_PHYSICALVALUE) = 0) THEN 1 ELSE 0 END FROM (SELECT SUM(POSTEDQTY) AS SUM_POSTEDQTY, SUM(POSTEDVALUE) AS SUM_POSTEDVALUE, SUM(DEDUCTED) AS SUM_DEDUCTED, SUM(RECEIVED) AS SUM_RECEIVED, SUM(RESERVORDERED) AS SUM_RESERVORDERED, SUM(RESERVPHYSICAL) AS SUM_RESERVPHYSICAL, SUM(ONORDER) AS SUM_ONORDER, SUM(ORDERED) AS SUM_ORDERED, SUM(QUOTATIONISSUE) AS SUM_QUOTATIONISSUE, SUM(QUOTATIONRECEIPT) AS SUM_QUOTATIONRECEIPT, SUM(REGISTERED) AS SUM_REGISTERED, SUM(PICKED) AS SUM_PICKED, SUM(AVAILORDERED) AS SUM_AVAILORDERED, SUM(AVAILPHYSICAL) AS SUM_AVAILPHYSICAL, SUM(PHYSICALVALUE) AS SUM_PHYSICALVALUE, SUM(ARRIVED) AS SUM_ARRIVED, SUM(PHYSICALINVENT) AS SUM_PHYSICALINVENT, SUM(POSTEDVALUESECCUR_RU) AS SUM_POSTEDVALUESECCUR_RU, SUM(PHYSICALVALUESECCUR_RU) AS SUM_PHYSICALVALUESECCUR_RU, MAX(LASTUPDDATEPHYSICAL) AS MAX_LASTUPDDATEPHYSICAL, MAX(LASTUPDDATEEXPECTED) AS MAX_LASTUPDDATEEXPECTED, INVENTSUMDELTA.ITEMID, INVENTSUMDELTA.INVENTDIMID FROM INVENTSUMDELTA WHERE INVENTSUMDELTA.DATAAREAID = @p2 AND INVENTSUMDELTA.PARTITION = @p4 AND INVENTSUMDELTA.TTSID = @p3 AND INVENTSUMDELTA.ISAGGREGATED = 0 GROUP BY INVENTSUMDELTA.ITEMID, INVENTSUMDELTA.INVENTDIMID) AS INVENTSUMDELTA WHERE INVENTSUM.DATAAREAID = @p1 AND INVENTSUM.PARTITION = @p4 AND INVENTSUM.ITEMID = INVENTSUMDELTA.ITEMID AND INVENTSUM.INVENTDIMID = INVENTSUMDELTA.INVENTDIMID     29\n", "Name: count, dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["#blocking_sql_df = voicemdf[voicemdf['Blking_SQL'].map(voicemdf['Blking_SQL'].value_counts()) > 5]\n", "#blocking_sql_df = voicemdf[voicemdf['Blking_SQL'].map(voicemdf['Blking_SQL'].value_counts()) > 5].sort_values(by='Blking_SQL', ascending=False)\n", "blocking_sql_df = voicemdf[voicemdf['Blking_SQL'].map(voicemdf['Blking_SQL'].value_counts()) > 5].sort_values(by='Blking_SQL', ascending=False)\n", "\n", "blocking_sql_df['Blking_SQL'].value_counts().head(5)\n", "#print(f\"{len(blocking_sql_df)}, {blocking_sql_df.values}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## First blocking query. Using cross joins on at least two big tables: inventsum and inventdim\n", "(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 nvarchar(5),@P5 int,@P6 bigint,@P7 nvarchar(5),@P8 bigint,@P9 int,@P10 int,@P11 int,@P12 int,@P13 int,@P14 int,@P15 int,@P16 int,@P17 int)\n", "SELECT \n", "    T1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,T1.<PERSON>EMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,\n", "    T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU,<PERSON>UM(T2.POSTEDQTY),<PERSON>UM(T2.POSTEDVALUE),<PERSON>UM(T2.PHYSICALVALUE),SUM(T2.DEDUCTED),SUM(T2.RECEIVED),SUM(T2.RESERVPHYSICAL),SUM(T2.RESERVORDERED),SUM(T2.REGISTERED),<PERSON>UM(T2.PICKED),<PERSON>UM(T2.ONORDER),<PERSON>UM(T2.ORDERED),<PERSON>UM(T2.ARRIVED),SUM(T2.QUOTATIONRECEIPT),SUM(T2.QUOTATIONISSUE),SUM(T2.AVAILPHYSICAL),SUM(T2.AVAILORDERED),SUM(T2.PHYSICALINVENT),<PERSON>UM(T2.POSTEDVALUESECCUR_RU),<PERSON>UM(T2.PHYSICALVALUESECCUR_RU) \n", "FROM \n", "    INVENTSUMDELTADIM T1 \n", "    CROSS JOIN INVENTSUM T2 \n", "    CROSS JOIN INVENTDIM T3 \n", "WHERE \n", "    ((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (((T2.PARTITION=@P3) AND (T2.DATAAREAID=@P4)) AND ((T2.ITEMID=T1.ITEMID) AND (T2.CLOSED=@P5))) AND (((T3.PARTITION=@P6) \n", "    AND (T3.DATAAREAID=@P7)) AND (((((((((((T2.INVENTDIMID=T3.INVENTDIMID) AND (T1.TTSID=@P8)) AND (T1.AREALLACTIVEDIMENSIONSSPECIFIED=@P9)) AND (T1.CHECKTYPE<@P10)) \n", "    AND ((T1.INVENTSIZEID=T3.INVENTSIZEID) OR (T1.INVENTSIZEIDFLAG=@P11))) AND ((T1.INVENTCOLORID=T3.INVENTCOLORID) OR (T1.INVENTCOLORIDFLAG=@P12))) \n", "    AND ((T1.INVENTSITEID=T3.INVENTSITEID) OR (T1.INVENTSITEIDFLAG=@P13))) AND ((T1.INVENTLOCATIONID=T3.INVENTLOCATIONID) OR (T1.INVENTLOCATIONIDFLAG=@P14))) \n", "    AND ((T1.LICENSEPLATEID=T3.LICENSEPLATEID) OR (T1.LICENSEPLATEFLAG=@P15))) AND ((T1.INVENTSTATUSID=T3.INVENTSTATUSID) OR (T1.INVENTSTATUSFLAG=@P16))) \n", "    AND ((T1.INVENTSERIALID=T3.INVENTSERIALID) OR (T1.INVENTSERIALIDFLAG=@P17)))) \n", "GROUP BY \n", "    T1.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,T1.<PERSON>EMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,\n", "    T1.INVENTBATCHID,T1.WMSL<PERSON><PERSON><PERSON><PERSON>,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU ORDER BY T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,\n", "    T1.INVENT<PERSON>LORID,T1.INVENT<PERSON>YLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU OPTION(FORCE ORDER)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatGPT suggestion to eliminate the cross joins on query1\n", "SELECT \n", "    T1.<PERSON>EC<PERSON><PERSON><PERSON><PERSON>, T1.ITEMID, T1.CONFIGID, T1.INVENTSIZEID, T1.INVENTCOLORID,\n", "    T1.INVENTSTYLEID, T1.INVENTSITEID, T1.INVENTLOCATIONID, T1.INVENTSTATUSID,\n", "    T1.LICENSEPLATEID, T1.INVENTBATCHID, T1.WMSLOCATIONID, T1.INVENTSERIALID,\n", "    T1.INVENTGTDID_RU, T1.INVENTPROFILEID_RU, T1.INVENTOWNERID_RU,\n", "    SUM(T2.POSTEDQTY), SUM(T2.POSTEDVALUE), SUM(T2.PHYSICALVALUE),\n", "    SUM(T2.DEDUCTED), SUM(T2.RECEIVED), SUM(T2.RESERVPHYSICAL),\n", "    SUM(T2.RESERVORDERED), SUM(T2.REGISTERED), SUM(T2.PICKED),\n", "    SUM(T2.ONORDER), SUM(T2.ORDERED), SUM(T2.ARRIVED),\n", "    SUM(T2.QUOTATIONRECEIPT), SUM(T2.QUOTATIONISSUE),\n", "    SUM(T2.AVAILPHYSICAL), SUM(T2.AVAILORDERED),\n", "    SUM(T2.PHYSICALINVENT), SUM(T2.POSTEDVALUESECCUR_RU),\n", "    SUM(T2.PHYSICALVALUESECCUR_RU) \n", "FROM \n", "    INVENTSUMDELTADIM T1\n", "INNER JOIN \n", "    INVENTSUM T2 ON T1.ITEMID = T2.ITEMID AND T2.CLOSED = @P5\n", "INNER JOIN \n", "    INVENTDIM T3 ON T2.INVENTDIMID = T3.INVENTDIMID\n", "WHERE \n", "    T1.PARTITION = @P1 AND T1.DATAAREAID = @P2\n", "    AND T2.PARTITION = @P3 AND T2.DATAAREAID = @P4\n", "    AND T1.TTSID = @P8 AND T1.AREALLACTIVEDIMENSIONSSPECIFIED = @P9\n", "    AND T1.CHECKTYPE < @P10\n", "    AND (T1.INVENTSIZEID = T3.INVENTSIZEID OR T1.INVENTSIZEIDFLAG = @P11)\n", "    AND (T1.INVENTCOLORID = T3.INVENTCOLORID OR T1.INVENTCOLORIDFLAG = @P12)\n", "    AND (T1.INVENTSITEID = T3.INVENTSITEID OR T1.INVENTSITEIDFLAG = @P13)\n", "    AND (T1.INVENTLOCATIONID = T3.INVENTLOCATIONID OR T1.INVENTLOCATIONIDFLAG = @P14)\n", "    AND (T1.LICENSEPLATEID = T3.LICENSEPLATEID OR T1.LICENSEPLATEFLAG = @P15)\n", "    AND (T1.INVENTSTATUSID = T3.INVENTSTATUSID OR T1.INVENTSTATUSFLAG = @P16)\n", "    AND (T1.INVENTSERIALID = T3.INVENTSERIALID OR T1.INVENTSERIALIDFLAG = @P17)\n", "GROUP BY \n", "    T1.<PERSON>EC<PERSON><PERSON><PERSON><PERSON>, T1.ITEMID, T1.CONFIGID, T1.INVENTSIZEID, T1.INVENTCOLORID,\n", "    T1.INVENTSTYLEID, T1.INVENTSITEID, T1.INVENTLOCATIONID, T1.INVENTSTATUSID,\n", "    T1.LICENSEPLATEID, T1.INVENTBATCHID, T1.WMSLOCATIONID, T1.INVENTSERIALID,\n", "    T1.INVENTGTDID_RU, T1.INVENTPROFILEID_RU, T1.INVENTOWNERID_RU\n", "ORDER BY \n", "    T1.CHECKTY<PERSON><PERSON>, T1.ITEMID, T1.CONFIGID, T1.INVENTSIZEID,\n", "    T1.INVENTCOLORID, T1.INVENTSTYLEID, T1.INVENTSITEID,\n", "    T1.INVENTLOCATIONID, T1.INVENTSTATUSID, T1.LICENSEPLATEID,\n", "    T1.INVENTBATCHID, T1.WMSLOCATIONID, T1.INVENTSERIALID,\n", "    T1.INVENTGTDID_RU, T1.INVENTPROFILEID_RU, T1.INVENTOWNERID_RU\n", "OPTION(FORCE ORDER);\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# X++ code\n", "            select forceSelectOrder inventSumDeltaDim\n", "                join #InventSumFields from inventSum\n", "                where inventSum.ItemId == inventSumDeltaDim.ItemId\n", "                    && inventSum.Closed == NoYes::No\n", "                    && (mustUseOnHandAmounts || inventSum.ClosedQty == NoYes::No) // If not needing amounts then only include those with open quantity\n", "                join inventDim\n", "                    group by inventSumDeltaDim.CheckType,\n", "                                inventSumDeltaDim.ItemId,\n", "                                inventSumDeltaDim.ConfigId,\n", "                                inventSumDeltaDim.InventSizeId,\n", "                                inventSumDeltaDim.InventColorId,\n", "                                inventSumDeltaDim.InventStyleId,\n", "                                inventSumDeltaDim.InventSiteId,\n", "                                inventSumDeltaDim.InventLocationId,\n", "                                // Added License Plate and Status\n", "                                inventSumDeltaDim.InventStatusId,\n", "                                inventSumDeltaDim.LicensePlateId,\n", "                                inventSumDeltaDim.InventBatchId,\n", "                                inventSumDeltaDim.wmsLocationId,\n", "                                inventSumDeltaDim.wmsPalletId,\n", "                                // <GEERU>\n", "                                inventSumDeltaDim.InventSerialId,\n", "                                inventSumDeltaDim.InventGTDId_RU,\n", "                                inventSumDeltaDim.InventProfileId_RU,\n", "                                inventSumDeltaDim.InventOwnerId_RU\n", "                                // </GEERU>\n", "                        where inventSum.InventDimId == inventDim.InventDimId &&\n", "                              inventSumDeltaDim.ttsId                   == this.ttsId()                         &&\n", "                              inventSumDeltaDim.AreAllActiveDimensionsSpecified == NoYes::No                    &&\n", "                              inventSumDeltaDim.checkType < InventOnhandCheckType::WHSReservation               &&\n", "                             (inventSumDeltaDim.InventSizeId            == inventDim.InventSizeId       ||\n", "                              inventSumDeltaDim.InventSizeIdFlag        == NoYes::No)                           &&\n", "                             (inventSumDeltaDim.InventColorId           == inventDim.InventColorId      ||\n", "                              inventSumDeltaDim.InventColorIdFlag       == NoYes::No)                           &&\n", "                             (inventSumDeltaDim.InventSiteId            == inventDim.InventSiteId       ||\n", "                              inventSumDeltaDim.InventSiteIdFlag        == NoYes::No)                           &&\n", "                             (inventSumDeltaDim.InventLocationId        == inventDim.InventLocationId   ||\n", "                              inventSumDeltaDim.InventLocationIdFlag    == NoYes::No)                           &&\n", "                             // Add license plate and inventory status\n", "                             (inventSumDeltaDim.LicensePlateId          == inventDim.LicensePlateId     ||\n", "                              inventSumDeltaDim.LicensePlateFlag        == NoYes::No)                           &&\n", "                             (inventSumDeltaDim.InventStatusId          == inventDim.InventStatusId     ||\n", "                              inventSumDeltaDim.InventStatusFlag        == NoYes::No)                           &&\n", "                             (inventSumDeltaDim.InventSerialId          == inventDim.InventSerialId     ||\n", "                              inventSumDeltaDim.InventSerialIdFlag      == NoYes::No);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "I'll do my best to assist you with optimizing the X++ query and address the cross join issue. However, I'm unable to execute or test code directly. I'll provide guidance based on best practices and potential optimization strategies.\n", "\n", "Here's a breakdown of the code and potential optimization steps:\n", "\n", "1. Indentation and Clarity:\n", "Improve code readability by properly indenting the where clauses and grouping them logically.\n", "Consider adding comments to explain the intention of different parts of the query.\n", "\n", "2. Join Order:\n", "The current join order might be causing the unintended cross join.\n", "Experiment with different join orders to see if it impacts the generated SQL.\n", "Consider starting with the most restrictive join (likely inventSum to inventDim based on InventDimId) and then joining inventSumDeltaDim.\n", "\n", "3. Explicit Join Conditions:\n", "Double-check that all intended join conditions are explicitly specified.\n", "The join inventDim line lacks a clear join condition, potentially leading to the cross join.\n", "Add an explicit where clause with the desired join condition between inventSum and inventDim.\n", "\n", "4. Grouping and Filtering:\n", "Reassess the necessity of the group by clause. If not essential for aggregation, removing it could improve performance.\n", "Evaluate if any filters can be applied earlier in the query to reduce the amount of data being processed.\n", "\n", "5. Indexing:\n", "Ensure appropriate indexes exist on the tables involved in the joins and filtering conditions.\n", "Missing or inadequate indexes can significantly impact performance.\n", "\n", "6. Query Hints:\n", "If necessary, consider using query hints to influence the SQL Server optimizer's behavior.\n", "Use hints cautiously and only after thorough testing.\n", "\n", "7. Alternative Approaches:\n", "Explore alternative query structures or data retrieval methods if the optimization efforts don't yield sufficient improvements.\n", "Consider using temporary tables or views to break down complex queries.\n", "Additional Recommendations:\n", "\n", "Use SQL Server Profiler or execution plans to visualize the generated SQL and identify bottlenecks.\n", "Test optimizations in a non-production environment before deploying to production.\n", "Consult with experienced AX developers or database administrators for further assistance if needed."]}, {"cell_type": "markdown", "metadata": {}, "source": ["The X++ code you provided contains a forceSelectOrder hint in the select statement. This hint is used to influence the join order in the resulting SQL query generated by the Dynamics AX query engine. However, the forceSelectOrder hint might not always guarantee that the SQL Server's query optimizer will interpret it as intended."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Option 1\n", "select forceSelectOrder inventSumDeltaDim\n", "    join #InventSumFields from inventSum\n", "    where inventSum.ItemId == inventSumDeltaDim.ItemId\n", "        && inventSum.Closed == NoYes::No\n", "        && (mustUseOnHandAmounts || inventSum.ClosedQty == NoYes::No)\n", "    join inventDim\n", "        where inventSum.InventDimId == inventDim.InventDimId &&\n", "            inventSumDeltaDim.ttsId == this.ttsId() &&\n", "            inventSumDeltaDim.AreAllActiveDimensionsSpecified == NoYes::No &&\n", "            inventSumDeltaDim.checkType < InventOnhandCheckType::WHSReservation &&\n", "            (inventSumDeltaDim.InventSizeId == inventDim.InventSizeId ||\n", "                inventSumDeltaDim.InventSizeIdFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventColorId == inventDim.InventColorId ||\n", "                inventSumDeltaDim.InventColorIdFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventSiteId == inventDim.InventSiteId ||\n", "                inventSumDeltaDim.InventSiteIdFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventLocationId == inventDim.InventLocationId ||\n", "                inventSumDeltaDim.InventLocationIdFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.LicensePlateId == inventDim.LicensePlateId ||\n", "                inventSumDeltaDim.LicensePlateFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventStatusId == inventDim.InventStatusId ||\n", "                inventSumDeltaDim.InventStatusFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventSerialId == inventDim.InventSerialId ||\n", "                inventSumDeltaDim.InventSerialIdFlag == NoYes::No);\n", "    group by inventSumDeltaDim.CheckType,\n", "        inventSumDeltaDim.ItemId,\n", "        inventSumDeltaDim.ConfigId,\n", "        inventSumDeltaDim.InventSizeId,\n", "        inventSumDeltaDim.InventColorId,\n", "        inventSumDeltaDim.InventStyleId,\n", "        inventSumDeltaDim.InventSiteId,\n", "        inventSumDeltaDim.InventLocationId,\n", "        inventSumDeltaDim.InventStatusId,\n", "        inventSumDeltaDim.LicensePlateId,\n", "        inventSumDeltaDim.InventBatchId,\n", "        inventSumDeltaDim.wmsLocationId,\n", "        inventSumDeltaDim.wmsPalletId,\n", "        inventSumDeltaDim.InventSerialId,\n", "        inventSumDeltaDim.InventGTDId_RU,\n", "        inventSumDeltaDim.InventProfileId_RU,\n", "        inventSumDeltaDim.InventOwnerId_RU;\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Option 2\n", "select forceSelectOrder inventSumDeltaDim\n", "    join #InventSumFields from inventSum\n", "    where inventSum.ItemId == inventSumDeltaDim.ItemId\n", "        && inventSum.Closed == NoYes::No\n", "        && (mustUseOnHandAmounts || inventSum.ClosedQty == NoYes::No)\n", "    exists join inventDim\n", "        where inventSum.InventDimId == inventDim.InventDimId &&\n", "            inventSumDeltaDim.ttsId == this.ttsId() &&\n", "            inventSumDeltaDim.AreAllActiveDimensionsSpecified == NoYes::No &&\n", "            inventSumDeltaDim.checkType < InventOnhandCheckType::WHSReservation &&\n", "            (inventSumDeltaDim.InventSizeId == inventDim.InventSizeId ||\n", "                inventSumDeltaDim.InventSizeIdFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventColorId == inventDim.InventColorId ||\n", "                inventSumDeltaDim.InventColorIdFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventSiteId == inventDim.InventSiteId ||\n", "                inventSumDeltaDim.InventSiteIdFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventLocationId == inventDim.InventLocationId ||\n", "                inventSumDeltaDim.InventLocationIdFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.LicensePlateId == inventDim.LicensePlateId ||\n", "                inventSumDeltaDim.LicensePlateFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventStatusId == inventDim.InventStatusId ||\n", "                inventSumDeltaDim.InventStatusFlag == NoYes::No) &&\n", "            (inventSumDeltaDim.InventSerialId == inventDim.InventSerialId ||\n", "                inventSumDeltaDim.InventSerialIdFlag == NoYes::No);\n", "    group by inventSumDeltaDim.CheckType,\n", "        inventSumDeltaDim.ItemId,\n", "        inventSumDeltaDim.ConfigId,\n", "        inventSumDeltaDim.InventSizeId,\n", "        inventSumDeltaDim.InventColorId,\n", "        inventSumDeltaDim.InventStyleId,\n", "        inventSumDeltaDim.InventSiteId,\n", "        inventSumDeltaDim.InventLocationId,\n", "        inventSumDeltaDim.InventStatusId,\n", "        inventSumDeltaDim.LicensePlateId,\n", "        inventSumDeltaDim.InventBatchId,\n", "        inventSumDeltaDim.wmsLocationId,\n", "        inventSumDeltaDim.wmsPalletId,\n", "        inventSumDeltaDim.InventSerialId,\n", "        inventSumDeltaDim.InventGTDId_RU,\n", "        inventSumDeltaDim.InventProfileId_RU,\n", "        inventSumDeltaDim.InventOwnerId_RU;\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Second query blocking(query2)\n", "(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 nvarchar(5),@P5 bigint)\n", "SELECT \n", "    T1.ITEMID,T1.INVENTDIMID,T1.RECID FROM INVENTSUM T1 WITH (UPDLOCK) \n", "CROSS JOIN INVENTSUMDELTADIM T2 \n", "WHERE ((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (((T2.PARTITION=@P3) AND (T2.DATAAREAID=@P4)) AND (((T2.ITEMID=T1.ITEMID) AND (T2.INVENTDIMID=T1.INVENTDIMID)) AND (T2.TTSID=@P5)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatGPT suggesstion(it might be mising the partition and dataareaid fields on the join)\n", "SELECT \n", "    T1.ITEMID, T1.INVENTDIMID, T1.RECID\n", "FROM \n", "    INVENTSUM T1 WITH (UPDLOCK)\n", "INNER JOIN \n", "    INVENTSUMDELTADIM T2 ON \n", "        T1.ITEMID = T2.ITEMID AND\n", "        T1.INVENTDIMID = T2.INVENTDIMID\n", "WHERE \n", "    T1.PARTITION = @P1 AND T1.DATAAREAID = @P2\n", "    AND T2.PARTITION = @P3 AND T2.DATAAREAID = @P4\n", "    AND T2.TTSID = @P5;"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Note on the 'WITH (UPDLOCK)'\n", "The WITH (UPDLOCK) hint is used to request an update lock on the selected rows from the INVENTSUM table. When a transaction acquires an update lock, it prevents other transactions from acquiring shared locks on the same rows. This, in turn, prevents other transactions from reading or updating the same rows concurrently.\n", "\n", "So, when this query is running, other transactions trying to update the same rows in the INVENTSUM table will be blocked until the transaction with the update lock either commits or rolls back. This helps avoid potential conflicts that could arise if multiple transactions were trying to modify the same data simultaneously.\n", "\n", "It's important to use lock hints judiciously, as they directly impact concurrency. In some cases, the default locking behavior provided by the SQL Server query optimizer is sufficient, and manual lock hints may not be necessary. The decision to use lock hints should be based on a thorough understanding of the application's requirements and the potential impact on concurrent operations."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Third blocking query. This one is updating, not selecting(pretty big, btw)\n", "(@p1 NVARCHAR(4), @p2 NVARCHAR(4), @p3 BIGINT, @p4 BIGINT)\n", "UPDATE INVENTSUM \n", "SET \n", "    INVENTSUM.POSTEDQTY = INVENTSUM.POSTEDQTY + INVENTSUMDELTA.SUM_POSTEDQTY\n", "    , INVENTSUM.POSTEDVALUE = INVENTSUM.POSTEDVALUE + INVENTSUMDELTA.SUM_POSTEDVALUE\n", "    , INVENTSUM.DEDUCTED = INVENTSUM.DEDUCTED + INVENTSUMDELTA.SUM_DEDUCTED\n", "    , INVENTSUM.RECEIVED = INVENTSUM.RECEIVED + INVENTSUMDELTA.SUM_RECEIVED\n", "    , INVENTSUM.RESERVORDERED = INVENTSUM.RESERVORDERED + INVENTSUMDELTA.SUM_RESERVORDERED\n", "    , INVENTSUM.RESERVPHYSICAL = INVENTSUM.RESERVPHYSICAL + INVENTSUMDELTA.SUM_RESERVPHYSICAL\n", "    , INVENTSUM.ONORDER = INVENTSUM.ONORDER + INVENTSUMDELTA.SUM_ONORDER\n", "    , INVENTSUM.ORDERED = INVENTSUM.ORDERED + INVENTSUMDELTA.SUM_ORDERED\n", "    , INVENTSUM.QUOTATIONISSUE = INVENTSUM.QUOTATIONISSUE + INVENTSUMDELTA.SUM_QUOTATIONISSUE\n", "    , INVENTSUM.QUOTATIONRECEIPT = INVENTSUM.QUOTATIONRECEIPT + INVENTSUMDELTA.SUM_QUOTATIONRECEIPT\n", "    , INVENTSUM.REGISTERED = INVENTSUM.REGISTERED + INVENTSUMDELTA.SUM_REGISTERED\n", "    , INVENTSUM.PICKED = INVENTSUM.PICKED + INVENTSUMDELTA.SUM_PICKED\n", "    , INVENTSUM.AVAILORDERED = INVENTSUM.AVAILORDERED + INVENTSUMDELTA.SUM_AVAILORDERED\n", "    , INVENTSUM.AVAILPHYSICAL = INVENTSUM.AVAILPHYSICAL + INVENTSUMDELTA.SUM_AVAILPHYSICAL\n", "    , INVENTSUM.PHYSICALVALUE = INVENTSUM.PHYSICALVALUE + INVENTSUMDELTA.SUM_PHYSICALVALUE\n", "    , INVENTSUM.ARRIVED = INVENTSUM.ARRIVED + INVENTSUMDELTA.SUM_ARRIVED\n", "    , INVENTSUM.PHYSICALINVENT = INVENTSUM.PHYSICALINVENT + INVENTSUMDELTA.SUM_PHYSICALINVENT\n", "    , INVENTSUM.PHYSICALVALUESECCUR_RU = INVENTSUM.PHYSICALVALUESECCUR_RU + INVENTSUMDELTA.SUM_PHYSICALVALUESECCUR_RU\n", "    , INVENTSUM.POSTEDVALUESECCUR_RU = INVENTSUM.POSTEDVALUESECCUR_RU + INVENTSUMDELTA.SUM_POSTEDVALUESECCUR_RU\n", "    , INVENTSUM.LAST<PERSON>DDATEPHYSICAL = CASE WHEN INVENTSUM.LASTUPDDATEPHYSICAL > INVENTSUMDELTA.MAX_LASTUPDDATEPHYSICAL THEN INVENTSUM.LASTUPD<PERSON><PERSON>PH<PERSON>SICAL ELSE INVENTSUMDELTA.MAX_LASTUPDDATEPHYSICAL END \n", "    , INVENTSUM.LASTUPDDATEEXPECTED = CASE WHEN INVENTSUM.LASTUPDDATEEXPECTED > INVENTSUMDELTA.MAX_LASTUPDDATEEXPECTED THEN INVENTSUM.LASTUPDDATEEXPECTED ELSE INVENTSUMDELTA.MAX_LASTUPDDATEEXPECTED END \n", "    , INVENTSUM.MODIFIEDDATETIME = dateadd(ms, -datepart(ms,getutcdate())\n", "    , getutcdate()) \n", "    , INVENTSUM.CLOSEDQTY = CASE WHEN ((INVENTSUM.POSTEDQTY + INVENTSUMDELTA.SUM_POSTEDQTY) = 0 AND (INVENTSUM.DEDUCTED + INVENTSUMDELTA.SUM_DEDUCTED) = 0 AND (INVENTSUM.PICKED + INVENTSUMDELTA.  SUM_PICKED) = 0 AND (INVENTSUM.RECEIVED + INVENTSUMDELTA.SUM_RECEIVED) = 0 AND (INVENTSUM.REGISTERED + INVENTSUMDELTA.SUM_REGISTERED) = 0 AND (INVENTSUM.RESERVPHYSICAL + INVENTSUMDELTA.SUM_RESERVPHYSICAL) = 0 AND (INVENTSUM.RESERVORDERED + INVENTSUMDELTA.SUM_RESERVORDERED) = 0 AND (INVENTSUM.ONORDER + INVENTSUMDELTA.SUM_ONORDER) = 0 AND (INVENTSUM.ORDERED + INVENTSUMDEL<PERSON>.SUM_ORDERED) = 0 AND (INVENTSUM.ARRIVED + INVENTSUMDELTA.SUM_ARRIVED) = 0 AND (INVENTSUM.QUOTATIONISSUE + INVENTSUMDELTA.SUM_QUOTATIONISSUE) = 0 AND (INVENTSUM.QUOTATIONRECEIPT + INVENTSUMDELTA.SUM_QUOTATIONRECEIPT) = 0) THEN 1 ELSE 0 END\n", "    , INVENTSUM.CLOSED = CASE WHEN ((INVENTSUM.POSTEDQTY + INVENTSUMDELTA.SUM_POSTEDQTY) = 0 AND (INVENTSUM.DEDUCTED + INVENTSUMDELTA.SUM_DEDUCTED) = 0 AND (INVENTSUM.PICKED + INVENTSUMDELTA.SUM_PICKED) = 0 AND (INVENTSUM.RECEIVED + INVENTSUMDELTA.SUM_RECEIVED) = 0 AND (INVENTSUM.REGISTERED + INVENTSUMDELTA.SUM_REGISTERED) = 0 AND (INVENTSUM.RESERVPHYSICAL + INVENTSUMDELTA.SUM_RESERVPHYSICAL) = 0 AND (INVENTSUM.RESERVORDERED + INVENTSUMDELTA.SUM_RESERVORDERED) = 0 AND (INVENTSUM.ONORDER + INVENTSUMDELTA.SUM_ONORDER) = 0 AND (INVENTSUM.ORDERED + INVENTSUMDELTA.SUM_ORDERED) = 0 AND (INVENTSUM.ARRIVED + INVENTSUMDELTA.SUM_ARRIVED) = 0 AND (INVENTSUM.QUOTATIONISSUE + INVENTSUMDELTA.SUM_QUOTATIONISSUE) = 0 AND (INVENTSUM.QUOTATIONRECEIPT + INVENTSUMDELTA.SUM_QUOTATIONRECEIPT) = 0 AND (INVENTSUM.POSTEDVALUE + INVENTSUMDELTA.SUM_POSTEDVALUE) = 0 AND (INVENTSUM.PHYSICALVALUE + INVENTSUMDELTA.SUM_PHYSICALVALUE) = 0) THEN 1 ELSE 0 END \n", "FROM \n", "    (\n", "        SELECT \n", "            SUM(POSTEDQTY) AS SUM_POSTEDQTY\n", "            , SUM(POSTEDVALUE) AS SUM_POSTEDVALUE\n", "            , SUM(DEDUCTED) AS SUM_DEDUCTED\n", "            , SUM(RECEIVED) AS SUM_RECEIVED\n", "            , SUM(RESERVORDERED) AS SUM_RESERVORDERED\n", "            , SUM(RESERVPHYSICAL) AS SUM_RESERVPHYSICAL\n", "            , SUM(ONORDER) AS SUM_ONORDER\n", "            , SUM(ORDERED) AS SUM_ORDERED\n", "            , SUM(QUOTATIONISSUE) AS SUM_QUOTATIONISSUE\n", "            , SUM(QUOTATIONRECEIPT) AS SUM_QUOTATIONRECEIPT\n", "            , SUM(REGISTERED) AS SUM_REGISTERED\n", "            , SUM(PICKED) AS SUM_PICKED\n", "            , SUM(AVAILORDERED) AS SUM_AVAILORDERED\n", "            , SUM(AVAILPHYSICAL) AS SUM_AVAILPHYSICAL\n", "            , SUM(PHYSICALVALUE) AS SUM_PHYSICALVALUE\n", "            , SUM(ARRIVED) AS SUM_ARRIVED\n", "            , SUM(PHYSICALINVENT) AS SUM_PHYSICALINVENT\n", "            , SUM(POSTEDVALUESECCUR_RU) AS SUM_POSTEDVALUESECCUR_RU\n", "            , SUM(PHYSICALVALUESECCUR_RU) AS SUM_PHYSICALVALUESECCUR_RU\n", "            , MAX(LASTUPDDATEPHYSICAL) AS MAX_LASTUPDDATEPHYSICAL\n", "            , MAX(LASTUPDDATEEXPECTED) AS MAX_LASTUPDDATEEXPECTED\n", "            , INVENTSUMDELTA.ITEMID\n", "            , INVENTSUMDELTA.INVENTDIMID \n", "        FROM \n", "            INVENTSUMDELTA \n", "        WHERE \n", "            INVENTSUMDELTA.DATAAREAID = @p2 AND INVENTSUMDELTA.PARTITION = @p4 AND INVENTSUMDELTA.TTSID = @p3 AND INVENTSUMDELTA.ISAGGREGATED = 0 \n", "        GROUP BY INVENTSUMDELTA.ITEMID, INVENTSUMDELTA.INVENTDIMID) \n", "        AS INVENTSUMDELTA \n", "        WHERE \n", "            INVENTSUM.DATAAREAID = @p1 AND INVENTSUM.PARTITION = @p4 AND INVENTSUM.ITEMID = INVENTSUMDELTA.ITEMID AND INVENTSUM.INVENTDIMID = INVENTSUMDELTA.INVENTDIMID"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# This update code is at the class InventUpdateOnHand, and it's generated by a method called 'sqlUpdateInventSumStr'\n", "\n", "protected str sqlUpdateInventSumStr()\n", "{\n", "    str                 sqls1,sqls2;\n", "    str                 pct2;\n", "    str                 pct3;\n", "    str                 pct5;\n", "    str                 pct6;\n", "    str                 pct8;\n", "    str 5               sumPrefix                    = 'SUM_';\n", "    str 5               maxPrefix                    = 'MAX_';\n", "    str 256             tmpFieldName;\n", "\n", "    str                 sqls_base                    = 'UPDATE %1 SET %2 FROM (SELECT %3 FROM %4 WHERE %5 GROUP BY %6) AS %7 WHERE %8';\n", "    str 256             inventSumName                = new SysDictTable(tableNum(InventSum)).name(DbBackend::Sql);  // %1\n", "    str 256             inventSumDeltaName           = new SysDictTable(tableNum(InventSumDelta)).name(DbBackend::Sql);  // %4 and %7\n", "\n", "    str 256             tmpFieldNameUpdPhys          = new SysDictField(tableNum(InventSum),fieldNum(InventSum,LastUpdDatePhysical)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "    str 256             tmpFieldNameUpdExp           = new SysDictField(tableNum(InventSum),fieldNum(InventSum,LastUpdDateExpected)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "    str 256             tmpFieldNameItemId           = new SysDictField(tableNum(InventSum),fieldNum(InventSum,ItemId)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "    str 256             tmpFieldNameInventDimId      = new SysDictField(tableNum(InventSum),fieldNum(InventSum,InventDimId)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "\n", "    str 256             tmpFieldNameClosed           = new SysDictField(tableNum(InventSum),fieldNum(InventSum,Closed)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "    str 256             tmpFieldNameClosedQty        = new SysDictField(tableNum(InventSum),fieldNum(InventSum,ClosedQty)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "\n", "    str 256             tmpFieldNameModifiedDate     = new SysDictField(tableNum(InventSum),fieldNum(InventSum, ModifiedDateTime)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "    str 256             tmpFieldNameUpdPhysDelta     = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,LastUpdDatePhysical)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "    str 256             tmpFieldNameUpdExpDelta      = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,LastUpdDateExpected)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "    str 256             tmpFieldNameItemIdDelta      = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,ItemId)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "    str 256             tmpFieldNameInventDimIdDelta = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,InventDimId)).name(DbBackend::Sql,0,FieldNameGenerationMode::FieldList);\n", "\n", "    DictFieldGroup      fieldGrp                     = new DictFieldGroup(tableNum(InventSum), tableFieldgroupStr(InventSum, DeltaFields));\n", "    DictFieldGroup      deltaFieldGrp                = new DictFieldGroup(tableNum(InventSumDelta), tableFieldgroupStr(InventSumDelta, DeltaFields));\n", "    DictFieldGroup      fieldGrpQty                  = new DictFieldGroup(tableNum(InventSum), tableFieldgroupStr(InventSum, DeltaFieldsQty));\n", "    int                 h;\n", "    boolean             isFirstDBField;\n", "    str 50              sqlFieldTypeNameDataAreaId;\n", "    str 50              sqlFieldTypeNameTTSId;\n", "    str 50              sqlFieldTypeNamePartition;\n", "\n", "    #Define.SQLDataAreaIdType('NVARCHAR')\n", "    #Define.SQLTTSIdType('BIGINT')\n", "    #Define.SQLPartition('BIGINT')\n", "\n", "    // %2 Begin\n", "    // SET InventSum.PostedQty = InventSum.PostedQty + InventSumDelta.SUM_PostedQty, InventSum.PostedValue = InventSum.PostedValue + InventSumDelta.SUM_PostedValue,.....\n", "    for (h = 1; h <= fieldGrp.numberOfFields(); h++)\n", "    {\n", "        tmpFieldName = new SysDictField(tableNum(InventSum),fieldGrp.field(h)).name(DbBackend::Sql);\n", "        if (tmpFieldName) // fields might be disabled by config keys\n", "        {\n", "            pct2 += pct2 ? ', ' : '';\n", "            // Note: field names must be exactly the same on tables InventSum and InventSumDelta\n", "            pct2 += inventSumName + '.' + tmpFieldName + ' = ' + inventSumName + '.' + tmpFieldName + ' + ' + inventSumDeltaName + '.' + sumPrefix + tmpFieldName;\n", "        }\n", "    }\n", "\n", "    //  InventSum.LastUpdDatePhysical = CASE WHEN InventSum.LastUpdDatePhysical > InventSumDelta.MAX_LastUpdDatePhysical\n", "    //                                       THEN InventSum.LastUpdDatePhysical\n", "    //                                       ELSE InventSumDelta.MAX_LastUpdDatePhysical\n", "    //                                  END,\n", "    pct2 += ', ' + inventSumName + '.' + tmpFieldNameUpdPhys + ' = CASE WHEN '  + inventSumName + '.' + tmpFieldNameUpdPhys + ' > ' + inventSumDeltaName + '.' + maxPrefix + tmpFieldNameUpdPhys;\n", "    pct2 += ' THEN '  + inventSumName + '.' + tmpFieldNameUpdPhys + ' ELSE ' + inventSumDeltaName + '.' + maxPrefix + tmpFieldNameUpdPhys + ' END ';\n", "\n", "    //  InventSum.LastUpdDateExpected = CASE WHEN InventSum.LastUpdDateExpected > InventSumDelta.MAX_LastUpdDateExpected\n", "    //                                       THEN InventSum.LastUpdDateExpected\n", "    //                                       ELSE InventSumDelta.MAX_LastUpdDateExpected\n", "    //                                  END,\n", "    pct2 += ', ' + inventSumName + '.' + tmpFieldNameUpdExp + ' = CASE WHEN '  + inventSumName + '.' + tmpFieldNameUpdExp + ' > ' + inventSumDeltaName + '.' + maxPrefix + tmpFieldNameUpdExp;\n", "    pct2 += ' THEN '  + inventSumName + '.' + tmpFieldNameUpdExp + ' ELSE ' + inventSumDeltaName + '.' + maxPrefix + tmpFieldNameUpdExp + ' END ';\n", "\n", "    //  InventSum.ModifiedDateTime = dateadd(ms, -datepart(ms,getutcdate()), getutcdate()),\n", "    pct2 += ', '  + inventSumName + '.' + tmpFieldNameModifiedDate + ' = dateadd(ms, -datepart(ms,getutcdate()), getutcdate()) ';\n", "\n", "    //  InventSum.ClosedQty              = CASE WHEN ((InventSum.PostedQty + InventSumDelta.SUM_PostedQty) = 0 ANd\n", "    //                                                (InventSum.Deducted  + InventSumDelta.SUM_Deducted)  = 0 ANd\n", "    //                                                (InventSum.Picked    + InventSumDelta.SUM_Picked)    = 0 ANd\n", "    //                                                ....\n", "    //                                                ....\n", "    //                                               )\n", "    //                                          THEN 1\n", "    //                                          ELSE 0\n", "    //                                     END,\n", "    isFirstDBField = true;\n", "    pct2 += ', ' + inventSumName + '.' + tmpFieldNameClosedQty + ' = CASE WHEN (';\n", "    for (h = 1; h <= fieldGrpQty.numberOfFields(); h++)\n", "    {\n", "        tmpFieldName = new SysDictField(tableNum(InventSum),fieldGrpQty.field(h)).name(DbBackend::Sql);\n", "        if (tmpFieldName) // fields might be disabled by config keys\n", "        {\n", "            pct2 += isFirstDBField ? '' : ' AND ';\n", "            isFirstDBField = false;\n", "            // Note: field names must be exactly the same on tables InventSum and InventSumDelta\n", "            pct2 += '(' + inventSumName + '.' + tmpFieldName + ' + ' + inventSumDeltaName + '.' + sumPrefix + tmpFieldName + ') = 0';\n", "        }\n", "    }\n", "    pct2 += ') THEN 1 ELSE 0 END,';\n", "\n", "    //  InventSum.Closed                 = CASE WHEN ((InventSum.PostedValue      + InventSumDelta.SUM_PostedValue)    = 0 ANd\n", "    //                                                (InventSum.PhysicalValue    + InventSumDelta.SUM_PhysicalValue)  = 0 ANd\n", "    //                                                ....\n", "    //                                                ....\n", "    //                                               )\n", "    //                                          THEN 1\n", "    //                                          ELSE 0\n", "    //                                     ENd\n", "    isFirstDBField = true;\n", "    pct2 += inventSumName + '.' + tmpFieldNameClosed + ' = CASE WHEN (';\n", "    for (h = 1; h <= fieldGrpQty.numberOfFields(); h++)\n", "    {\n", "        tmpFieldName = new SysDictField(tableNum(InventSum),fieldGrpQty.field(h)).name(DbBackend::Sql);\n", "        if (tmpFieldName) // fields might be disabled by config keys\n", "        {\n", "            pct2 += isFirstDBField ? '' : ' AND ';\n", "            isFirstDBField = false;\n", "            // Note: field names must be exactly the same on tables InventSum and InventSumDelta\n", "            pct2 += '(' + inventSumName + '.' + tmpFieldName + ' + ' + inventSumDeltaName + '.' + sumPrefix + tmpFieldName + ') = 0';\n", "        }\n", "    }\n", "    tmpFieldName = new SysDictField(tableNum(InventSum),fieldNum(InventSum,PostedValue)).name(DbBackend::Sql);\n", "    pct2 += ' AND (' + inventSumName + '.' + tmpFieldName + ' + ' + inventSumDeltaName + '.' + sumPrefix + tmpFieldName + ') = 0';\n", "    tmpFieldName = new SysDictField(tableNum(InventSum),fieldNum(InventSum,PhysicalValue)).name(DbBackend::Sql);\n", "    pct2 += ' AND (' + inventSumName + '.' + tmpFieldName + ' + ' + inventSumDeltaName + '.' + sumPrefix + tmpFieldName + ') = 0';\n", "    pct2 += ') THEN 1 ELSE 0 END';\n", "    // %2 End\n", "\n", "    // %3 Begin\n", "    // SUM (PostedQty) AS SUM_PostedQty, SUM(PostedValue) AS SUM_PostedValue, .....\n", "    for (h=1;h<=deltaFieldGrp.numberOfFields();h++)\n", "    {\n", "        tmpFieldName = new SysDictField(tableNum(InventSumDelta),deltaFieldGrp.field(h)).name(DbBackend::Sql);\n", "        if (tmpFieldName) // fields might be disabled by config keys\n", "        {\n", "            pct3 += 'SUM(' + tmpFieldName + ') AS ' + sumPrefix + tmpFieldName + ', ';\n", "        }\n", "    }\n", "    // MAX (LastUpdDatePhysical) AS MAX_LastUpdDatePhysical, MAX (LastUpdDateExpected) AS MAX_LastUpdDateExpected\n", "    pct3 += 'MAX(' + tmpFieldNameUpdPhysDelta + ') AS ' + maxPrefix + tmpFieldNameUpdPhysDelta + ', ';\n", "    pct3 += 'MAX(' + tmpFieldNameUpdExpDelta + ') AS ' + maxPrefix + tmpFieldNameUpdExpDelta + ', ';\n", "\n", "    // InventSumDelta.ItemId, InventSumDelta.InventDimId\n", "    pct3 += inventSumDeltaName + '.' + tmpFieldNameItemIdDelta + ', ';\n", "    pct3 += inventSumDeltaName + '.' + tmpFieldNameInventDimIdDelta;\n", "    // %3 End\n", "\n", "    // %5 Begin\n", "    //      InventSumDelta.DataAreaId   = @p2\n", "    // AND  InventSumDelta.Partition    = @p4\n", "    // AND  InventSumDelta.TTSId        = @p3\n", "    // AND  InventSumDelta.IsAggregated = 0\n", "    tmpFieldName = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,DataAreaId)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct5 += inventSumDeltaName + '.' + tmpFieldName + ' = @p2';\n", "    pct5 += ' AND ';\n", "\n", "    tmpFieldName = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,Partition)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct5 += inventSumDeltaName + '.' + tmpFieldName + ' = @p4';\n", "    pct5 += ' AND ';\n", "\n", "    tmpFieldName = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,ttsId)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct5 += inventSumDeltaName + '.' + tmpFieldName + ' = ';\n", "    pct5 += '@p3';\n", "\n", "    pct5 += ' AND ';\n", "    tmpFieldName = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,IsAggregated)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct5 += inventSumDeltaName + '.' + tmpFieldName + ' = ';\n", "    pct5 += '0';\n", "    // %5 End\n", "\n", "    // %6 Begin\n", "    // InventSumDelta.ItemId, InventSumDelta.InventDimId\n", "    pct6 += inventSumDeltaName + '.' + tmpFieldNameItemIdDelta + ', ';\n", "    pct6 += inventSumDeltaName + '.' + tmpFieldNameInventDimIdDelta;\n", "    // %6 End\n", "\n", "    // %8 Begin\n", "    //          InventSum.DataAreaId        = @p1\n", "    //    AND   InventSum.Partition         = @p4\n", "    //    AND   InventSum.ItemId            = InventSumDelta.ItemId\n", "    //    AND   InventSum.InventDimId       = InventSumDelta.InventDimId\n", "    tmpFieldName = new SysDictField(tableNum(InventSum),fieldNum(InventSum,DataAreaId)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct8 += inventSumName + '.' + tmpFieldName + ' = ';\n", "    pct8 += '@p1' + ' AND ';\n", "\n", "    tmpFieldName = new SysDictField(tableNum(InventSum),fieldNum(InventSum,Partition)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct8 += inventSumName + '.' + tmpFieldName + ' = @p4';\n", "    pct8 += ' AND ';\n", "\n", "    tmpFieldName = new SysDictField(tableNum(InventSum),fieldNum(InventSum,ItemId)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct8 += inventSumName       + '.' + tmpFieldName      + ' = ';\n", "    tmpFieldName = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,ItemId)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct8 += inventSumDeltaName  + '.' + tmpFieldName + ' AND ';\n", "    tmpFieldName = new SysDictField(tableNum(InventSum),fieldNum(InventSum,InventDimId)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct8 += inventSumName       + '.' + tmpFieldName + ' = ';\n", "    tmpFieldName = new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,InventDimId)).name(DbBackend::Sql,0,FieldNameGenerationMode::Where<PERSON><PERSON>e);\n", "    pct8 += inventSumDeltaName  + '.' + tmpFieldNameInventDimIdDelta;\n", "    // %8 End\n", "\n", "    // find SQL field type names\n", "    sqlFieldTypeNameDataAreaId = #SQLDataAreaIdType + '(' + int2str(new SysDictField(tableNum(InventSumDelta),fieldNum(InventSumDelta,DataAreaId)).stringLen()) + ')';\n", "    sqlFieldTypeNameTTSId = #SQLTTSIdType;\n", "    sqlFieldTypeNamePartition = #SQLPartition;\n", "\n", "    // build final str\n", "    sqls1 = strFmt(sqls_base,inventSumName,pct2,pct3,inventSumDeltaName,pct5,pct6,inventSumDeltaName,pct8);\n", "    sqls2 = 'execute sp_executesql N' + '\\'' + sqls1 + '\\',N\\'@p1 ' + sqlFieldTypeNameDataAreaId +\n", "                                                           ', @p2 ' + sqlFieldTypeNameDataAreaId +\n", "                                                           ', @p3 ' + sqlFieldTypeNameTTSId +\n", "                                                           ', @p4 ' + sqlFieldTypeNamePartition +\n", "                                                           '\\',@p1=%1, @p2=%2, @p3=\\'%3\\', @p4=\\'%4\\'';\n", "\n", "    return sqls2;\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatGPT generated code\n", "protected str sqlUpdateInventSumStr()\n", "{\n", "    str sqls;\n", "    str sumPrefix = 'SUM_';\n", "    str maxPrefix = 'MAX_';\n", "    \n", "    DictFieldGroup fieldGrp = new DictFieldGroup(tableNum(InventSum), tableFieldgroupStr(InventSum, DeltaFields));\n", "    DictFieldGroup deltaFieldGrp = new DictFieldGroup(tableNum(InventSumDelta), tableFieldgroupStr(InventSumDelta, DeltaFields));\n", "    DictFieldGroup fieldGrpQty = new DictFieldGroup(tableNum(InventSum), tableFieldgroupStr(InventSum, DeltaFieldsQty));\n", "\n", "    str inventSumName = new SysDictTable(tableNum(InventSum)).name(DbBackend::Sql);\n", "    str inventSumDeltaName = new SysDictTable(tableNum(InventSumDelta)).name(DbBackend::Sql);\n", "\n", "    // Generate SET part of SQL statement\n", "    str setClause = getSetClause(fieldGrp, sumPrefix, maxPrefix);\n", "\n", "    // Generate SELECT part of SQL statement\n", "    str selectClause = getSelectClause(deltaFieldGrp, maxPrefix);\n", "\n", "    // Generate WHERE part of SQL statement\n", "    str whereClause = getWhereClause(fieldGrpQty);\n", "\n", "    // Build the final SQL statement\n", "    sqls = strFmt('UPDATE %1 SET %2 FROM (SELECT %3 FROM %4 WHERE %5) AS %6 WHERE %7',\n", "                  inventSumName, setClause, selectClause, inventSumDeltaName, where<PERSON><PERSON><PERSON>, inventSumDeltaName, getWhereClause(fieldGrp));\n", "\n", "    // Execute the SQL statement\n", "    str sqls2 = 'execute sp_executesql N' + '\\'' + sqls + '\\', N\\'@p1 NVARCHAR(50), @p2 NVARCHAR(50), @p3 BIGINT, @p4 BIGINT\\', @p1=%1, @p2=%2, @p3=%3, @p4=%4';\n", "\n", "    return sqls2;\n", "}\n", "\n", "// Helper method to generate SET clause\n", "private str getSetClause(DictFieldGroup fieldGrp, str sumPrefix, str maxPrefix)\n", "{\n", "    str <PERSON><PERSON><PERSON><PERSON>;\n", "\n", "    for (int i = 1; i <= fieldGrp.numberOfFields(); i++)\n", "    {\n", "        str fieldName = new SysDictField(tableNum(InventSum), fieldGrp.field(i)).name(DbBackend::Sql);\n", "\n", "        if (fieldName)\n", "        {\n", "            setClause += setClause ? ', ' : '';\n", "            setClause += strFmt('%1.%2 = %1.%2 + %3.%4', inventSumName, fieldName, inventSumDeltaName, sumPrefix + fieldName);\n", "        }\n", "    }\n", "\n", "    setClause += strFmt(', %1.%2 = CASE WHEN %1.%2 > %3.%4 THEN %1.%2 ELSE %3.%4 END', inventSumName, tmpFieldNameUpdPhys, inventSumDeltaName, maxPrefix + tmpFieldNameUpdPhys);\n", "    setClause += strFmt(', %1.%2 = CASE WHEN %1.%2 > %3.%4 THEN %1.%2 ELSE %3.%4 END', inventSumName, tmpFieldNameUpdExp, inventSumDeltaName, maxPrefix + tmpFieldNameUpdExp);\n", "    setClause += strFmt(', %1.%2 = dateadd(ms, -datepart(ms,getutcdate()), getutcdate())', inventSumName, tmpFieldNameModifiedDate);\n", "    setClause += strFmt(', %1.%2 = CASE WHEN (%3) THEN 1 ELSE 0 END', inventSumName, tmpFieldNameClosedQty, getClosedQtyCondition(fieldGrpQty));\n", "    setClause += strFmt(', %1.%2 = CASE WHEN (%3) THEN 1 ELSE 0 END', inventSumName, tmpFieldNameClosed, getClosedCondition(fieldGrpQty));\n", "\n", "    return setClause;\n", "}\n", "\n", "// Helper method to generate SELECT clause\n", "private str getSelectClause(DictFieldGroup deltaFieldGrp, str maxPrefix)\n", "{\n", "    str select<PERSON><PERSON><PERSON>;\n", "\n", "    for (int i = 1; i <= deltaFieldGrp.numberOfFields(); i++)\n", "    {\n", "        str fieldName = new SysDictField(tableNum(InventSumDelta), deltaFieldGrp.field(i)).name(DbBackend::Sql);\n", "\n", "        if (fieldName)\n", "        {\n", "            selectClause += strFmt('SUM(%1) AS %2, ', fieldName, sumPrefix + fieldName);\n", "        }\n", "    }\n", "\n", "    selectClause += strFmt('MAX(%1) AS %2, ', tmpFieldNameUpdPhysDelta, maxPrefix + tmpFieldNameUpdPhysDelta);\n", "    selectClause += strFmt('MAX(%1) AS %2, ', tmpFieldNameUpdExpDelta, maxPrefix + tmpFieldNameUpdExpDelta);\n", "    selectClause += strFmt('%1.%2, %1.%3', inventSumDeltaName, tmpFieldNameItemIdDelta, tmpFieldNameInventDimIdDelta);\n", "\n", "    return select<PERSON><PERSON><PERSON>;\n", "}\n", "\n", "// Helper method to generate WHERE clause\n", "private str getWhereClause(DictFieldGroup fieldGrpQty)\n", "{\n", "    str <PERSON><PERSON><PERSON><PERSON>;\n", "\n", "    whereClause += strFmt('%1.%2 = @p2 AND %1.%3 = @p4 AND %1.%5 = @p3 AND %1.%6 = 0',\n", "                          inventSumDeltaName, tmpFieldNameDataAreaId, tmpFieldNamePartition, ttsId, tmpFieldNameIsAggregated);\n", "\n", "    return where<PERSON><PERSON><PERSON>;\n", "}\n", "\n", "// Helper method to generate closed quantity condition\n", "private str getClosedQtyCondition(DictFieldGroup fieldGrpQty)\n", "{\n", "    str condition;\n", "\n", "    condition += strFmt('(%1)', getSumCondition(fieldGrpQty));\n", "\n", "    return condition;\n", "}\n", "\n", "// Helper method to generate closed condition\n", "private str getClosedCondition(DictFieldGroup fieldGrpQty)\n", "{\n", "    str condition;\n", "\n", "    condition += getSumCondition(fieldGrpQty);\n", "    condition += strFmt(' AND (%1 + %2) = 0', getPostedValueCondition(fieldGrpQty), getPhysicalValueCondition(fieldGrpQty));\n", "\n", "    return condition;\n", "}\n", "\n", "// Helper method to generate SUM condition\n", "private str getSumCondition(DictFieldGroup fieldGrpQty)\n", "{\n", "    str condition;\n", "\n", "    for (int i = 1; i <= fieldGrpQty.numberOfFields(); i++)\n", "    {\n", "        str fieldName = new SysDictField(tableNum(InventSum), fieldGrpQty.field(i)).name(DbBackend::Sql);\n", "\n", "        if (fieldName)\n", "        {\n", "            condition += condition ? ' AND ' : '';\n", "            condition += strFmt('(%1 + %2) = 0', inventSumName + '.' + fieldName, inventSumDeltaName + '.' + sumPrefix + fieldName);\n", "        }\n", "    }\n", "\n", "    return condition;\n", "}\n", "\n", "// Helper method to generate PostedValue condition\n", "private str getPostedValueCondition(DictFieldGroup fieldGrpQty)\n", "{\n", "    str condition;\n", "\n", "    str fieldName = new SysDictField(tableNum(InventSum), fieldNum(InventSum, PostedValue)).name(DbBackend::Sql);\n", "\n", "    condition += strFmt('(%1 + %2) = 0', inventSumName + '.' + fieldName, inventSumDeltaName + '.' + sumPrefix + fieldName);\n", "\n", "    return condition;\n", "}\n", "\n", "// Helper method to generate PhysicalValue condition\n", "private str getPhysicalValueCondition(DictFieldGroup fieldGrpQty)\n", "{\n", "    str condition;\n", "\n", "    str fieldName = new SysDictField(tableNum(InventSum), fieldNum(InventSum, PhysicalValue)).name(DbBackend::Sql);\n", "\n", "    condition += strFmt('(%1 + %2) = 0', inventSumName + '.' + fieldName, inventSumDeltaName + '.' + sumPrefix + fieldName);\n", "\n", "    return condition;\n", "}\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Blked_Lines</th>\n", "      <th>Blking_AXUser</th>\n", "      <th>Blking_Host</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>178</td>\n", "      <td>svc-vite</td>\n", "      <td>PRODAIFAOS02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>376</th>\n", "      <td>135</td>\n", "      <td>NaN</td>\n", "      <td>PRODWMSAOS02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>233</th>\n", "      <td>132</td>\n", "      <td>NaN</td>\n", "      <td>PRODWMSAOS01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>494</th>\n", "      <td>111</td>\n", "      <td>svc-vite</td>\n", "      <td>PRODAIFAOS02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>109</td>\n", "      <td>svc-vite</td>\n", "      <td>PRODAIFAOS02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>339</th>\n", "      <td>1</td>\n", "      <td>svc-vite</td>\n", "      <td>PRODAIFAOS02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>327</th>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>PRODWMSAOS02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>322</th>\n", "      <td>1</td>\n", "      <td>svc-auto</td>\n", "      <td>PRODUSRAOS01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>481</th>\n", "      <td>1</td>\n", "      <td>svc-auto</td>\n", "      <td>PRODUSRAOS01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>1</td>\n", "      <td>svc-vite</td>\n", "      <td>PRODAIFAOS02</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>378 rows × 3 columns</p>\n", "</div>"], "text/plain": ["     Blked_Lines Blking_AXUser   Blking_Host\n", "214          178      svc-vite  PRODAIFAOS02\n", "376          135           NaN  PRODWMSAOS02\n", "233          132           NaN  PRODWMSAOS01\n", "494          111      svc-vite  PRODAIFAOS02\n", "150          109      svc-vite  PRODAIFAOS02\n", "..           ...           ...           ...\n", "339            1      svc-vite  PRODAIFAOS02\n", "327            1           NaN  PRODWMSAOS02\n", "322            1      svc-auto  PRODUSRAOS01\n", "481            1      svc-auto  PRODUSRAOS01\n", "35             1      svc-vite  PRODAIFAOS02\n", "\n", "[378 rows x 3 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "blkingdf = voicemdf[voicemdf['Blocking_SQL']=='(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 nvarchar(5),@P5 int,@P6 bigint,@P7 nvarchar(5),@P8 bigint,@P9 int,@P10 int,@P11 int,@P12 int,@P13 int,@P14 int,@P15 int,@P16 int,@P17 int)SELECT T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU,<PERSON>UM(T2.POSTEDQTY),<PERSON><PERSON>(T2.POSTEDVALUE),<PERSON><PERSON>(T2.PHYSICALVALUE),<PERSON>UM(T2.DEDUCTED),SUM(T2.RECEIVED),SUM(T2.RESERVPHYSICAL),SUM(T2.RESERVORDERED),SUM(T2.REGISTERED),SUM(T2.PICKED),SUM(T2.ONORDER),SUM(T2.ORDERED),SUM(T2.ARRIVED),SUM(T2.QUOTATIONRECEIPT),SUM(T2.QUOTATIONISSUE),SUM(T2.AVAILPHYSICAL),SUM(T2.AVAILORDERED),SUM(T2.PHYSICALINVENT),SUM(T2.POSTEDVALUESECCUR_RU),SUM(T2.PHYSICALVALUESECCUR_RU) FROM INVENTSUMDELTADIM T1 CROSS JOIN INVENTSUM T2 CROSS JOIN INVENTDIM T3 WHERE ((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (((T2.PARTITION=@P3) AND (T2.DATAAREAID=@P4)) AND ((T2.ITEMID=T1.ITEMID) AND (T2.CLOSED=@P5))) AND (((T3.PARTITION=@P6) AND (T3.DATAAREAID=@P7)) AND (((((((((((T2.INVENTDIMID=T3.INVENTDIMID) AND (T1.TTSID=@P8)) AND (T1.AREALLACTIVEDIMENSIONSSPECIFIED=@P9)) AND (T1.CHECKTYPE<@P10)) AND ((T1.INVENTSIZEID=T3.INVENTSIZEID) OR (T1.INVENTSIZEIDFLAG=@P11))) AND ((T1.INVENTCOLORID=T3.INVENTCOLORID) OR (T1.INVENTCOLORIDFLAG=@P12))) AND ((T1.INVENTSITEID=T3.INVENTSITEID) OR (T1.INVENTSITEIDFLAG=@P13))) AND ((T1.INVENTLOCATIONID=T3.INVENTLOCATIONID) OR (T1.INVENTLOCATIONIDFLAG=@P14))) AND ((T1.LICENSEPLATEID=T3.LICENSEPLATEID) OR (T1.LICENSEPLATEFLAG=@P15))) AND ((T1.INVENTSTATUSID=T3.INVENTSTATUSID) OR (T1.INVENTSTATUSFLAG=@P16))) AND ((T1.INVENTSERIALID=T3.INVENTSERIALID) OR (T1.INVENTSERIALIDFLAG=@P17)))) GROUP BY T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU ORDER BY T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU OPTION(FORCE ORDER)']\n", "blkingdf.loc[:, ['Blked_Lines', 'Blking_AXUser', 'Blking_Host']].sort_values(by='Blked_Lines', ascending=False)\n", "#blkingdf = blkingdf"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["Blking_Host\n", "PRODAIFAOS02    185\n", "PRODBCHAOS01     78\n", "PRODWMSAOS01     46\n", "PRODUSRAOS01     44\n", "PRODWMSAOS02     10\n", "PRODUSRAOS02      3\n", "PRODBCHAOS04      2\n", "PRODAIFAOS01      2\n", "PRODBCHAOS02      1\n", "Name: count, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["voicemdf['Blking_Host'].value_counts()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}