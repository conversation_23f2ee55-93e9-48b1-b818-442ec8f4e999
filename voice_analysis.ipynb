import pandas as pd
import matplotlib.pyplot as plt

#voicemdf

voicemdf = pd.read_csv("VoiceBlkMon.csv")
voicemdf.columns.tolist

shape = voicemdf.shape
print(f"Rows: {shape[0]:,}, Columns: {shape[1]}")

voicemdf.info()

voicemdf = voicemdf.astype({'Blked_AXSessionID': 'Int64', 'Blked_SQLSessionID': 'Int64', 'Blked_Lines': 'Int64','Blking_AXSessionID':'Int64',
                            'Blking_SQLSessionID':'Int64', 'Max_TimeInSec': 'Int64'})
voicemdf.info()

top_5_blocked_queries = voicemdf['Blked_SQL'].value_counts().head(5)
top_5_blocked_queries


# Count the occurrences of each unique SQL query in the 'Blking_SQL' column
query_counts = voicemdf['Blking_SQL'].value_counts()

# Total rows before filtering
total_rows = len(voicemdf)

# Filter queries occurring more than five times
filtered_queries = query_counts[query_counts > 5]

# Plot a bar chart
plt.bar(range(len(filtered_queries)), filtered_queries.values, align='center')
plt.xlabel('Query')
plt.ylabel('Total Occurrences')
plt.title('Top Queries Involved in Blocking (Occurrences > 5)')

# Annotate with exact numbers on top of each bar
#for i, count in enumerate(filtered_queries.values):
 #   plt.text(i, count + 0.02 * total_rows, f"{count}/{total_rows}", ha='center', va='bottom', rotation=45)

plt.xticks(range(len(filtered_queries)), ['Query{}'.format(i+1) for i in range(len(filtered_queries))])

# Adjust legend position
plt.legend(['Occurrences'], bbox_to_anchor=(0.5, -0.15), loc='upper center', borderaxespad=0.)

# Save the figure with adjusted layout
plt.tight_layout()
plt.savefig('bar_chart.png', bbox_inches='tight')

# Display the plot
plt.show()



# Count the occurrences of each unique SQL query in the 'Blking_SQL' column
query_counts = voicemdf['Blking_SQL'].value_counts()

# Total rows before filtering
total_rows = len(voicemdf)

# Filter queries occurring more than five times
filtered_queries = query_counts[query_counts > 5]

# Create subplots with extra height for the legend
fig, ax = plt.subplots(figsize=(12, 9))
plt.subplots_adjust(bottom=0.2)

# Plot a bar chart
ax.bar(range(len(filtered_queries)), filtered_queries.values, align='center')
ax.set_xlabel('Query')
ax.set_ylabel('Total Occurrences')
ax.set_title('Top Queries Involved in Blocking (Occurrences > 5)')

# Annotate with exact numbers on top of each bar
for i, count in enumerate(filtered_queries.values):
    ax.text(i, count + 0.02 * total_rows, f"{count}/{total_rows}", ha='center', va='bottom', rotation=0)

ax.set_xticks(range(len(filtered_queries)))
ax.set_xticklabels(['Query{}'.format(i+1) for i in range(len(filtered_queries))])

# Add legend outside the plot
ax.legend(['Occurrences'], bbox_to_anchor=(1.05, 1), loc='upper left')

# Save the figure with adjusted layout
plt.tight_layout()
plt.savefig('bar_chart.png', bbox_inches='tight')

# Display the plot
plt.show()



#blocking_sql_df = voicemdf[voicemdf['Blking_SQL'].map(voicemdf['Blking_SQL'].value_counts()) > 5]
#blocking_sql_df = voicemdf[voicemdf['Blking_SQL'].map(voicemdf['Blking_SQL'].value_counts()) > 5].sort_values(by='Blking_SQL', ascending=False)
blocking_sql_df = voicemdf[voicemdf['Blking_SQL'].map(voicemdf['Blking_SQL'].value_counts()) > 5].sort_values(by='Blking_SQL', ascending=False)

blocking_sql_df['Blking_SQL'].value_counts().head(5)
#print(f"{len(blocking_sql_df)}, {blocking_sql_df.values}")


blkingdf = voicemdf[voicemdf['Blocking_SQL']=='(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 nvarchar(5),@P5 int,@P6 bigint,@P7 nvarchar(5),@P8 bigint,@P9 int,@P10 int,@P11 int,@P12 int,@P13 int,@P14 int,@P15 int,@P16 int,@P17 int)SELECT T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU,SUM(T2.POSTEDQTY),SUM(T2.POSTEDVALUE),SUM(T2.PHYSICALVALUE),SUM(T2.DEDUCTED),SUM(T2.RECEIVED),SUM(T2.RESERVPHYSICAL),SUM(T2.RESERVORDERED),SUM(T2.REGISTERED),SUM(T2.PICKED),SUM(T2.ONORDER),SUM(T2.ORDERED),SUM(T2.ARRIVED),SUM(T2.QUOTATIONRECEIPT),SUM(T2.QUOTATIONISSUE),SUM(T2.AVAILPHYSICAL),SUM(T2.AVAILORDERED),SUM(T2.PHYSICALINVENT),SUM(T2.POSTEDVALUESECCUR_RU),SUM(T2.PHYSICALVALUESECCUR_RU) FROM INVENTSUMDELTADIM T1 CROSS JOIN INVENTSUM T2 CROSS JOIN INVENTDIM T3 WHERE ((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (((T2.PARTITION=@P3) AND (T2.DATAAREAID=@P4)) AND ((T2.ITEMID=T1.ITEMID) AND (T2.CLOSED=@P5))) AND (((T3.PARTITION=@P6) AND (T3.DATAAREAID=@P7)) AND (((((((((((T2.INVENTDIMID=T3.INVENTDIMID) AND (T1.TTSID=@P8)) AND (T1.AREALLACTIVEDIMENSIONSSPECIFIED=@P9)) AND (T1.CHECKTYPE<@P10)) AND ((T1.INVENTSIZEID=T3.INVENTSIZEID) OR (T1.INVENTSIZEIDFLAG=@P11))) AND ((T1.INVENTCOLORID=T3.INVENTCOLORID) OR (T1.INVENTCOLORIDFLAG=@P12))) AND ((T1.INVENTSITEID=T3.INVENTSITEID) OR (T1.INVENTSITEIDFLAG=@P13))) AND ((T1.INVENTLOCATIONID=T3.INVENTLOCATIONID) OR (T1.INVENTLOCATIONIDFLAG=@P14))) AND ((T1.LICENSEPLATEID=T3.LICENSEPLATEID) OR (T1.LICENSEPLATEFLAG=@P15))) AND ((T1.INVENTSTATUSID=T3.INVENTSTATUSID) OR (T1.INVENTSTATUSFLAG=@P16))) AND ((T1.INVENTSERIALID=T3.INVENTSERIALID) OR (T1.INVENTSERIALIDFLAG=@P17)))) GROUP BY T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU ORDER BY T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU OPTION(FORCE ORDER)']
blkingdf.loc[:, ['Blked_Lines', 'Blking_AXUser', 'Blking_Host']].sort_values(by='Blked_Lines', ascending=False)
#blkingdf = blkingdf

voicemdf['Blking_Host'].value_counts()