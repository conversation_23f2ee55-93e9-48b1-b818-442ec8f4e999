import pyodbc
#import datetime
import csv
import os
import time
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.utils import make_msgid

# Email alert threshold
alert_blk_lines =   3   # x lines blocked will trigger the alert
alert_wait_time =  30  # More than x seconds will do the same
orders_treshold = 50 # Send an alert if more than x orders are pending
picks_treshold = 250 # Send an alert if more than x picks are pending

intCheckIntervalInSeconds = 60 # Check every minute

def send_email_alert(subject, body):
    # Email configuration
    domain          = 'hannaandersson.com'
    sender          = 'no-reply'
    sender_email    = sender + '@' + domain
    receiver_email  = "labreu" + '@' + domain
    
    # Get the email password from the environment variable, stored in LastPass
    password = os.environ.get("noreplykey")
    
    # Check if the password is set
    if password is None:
        print("Error: Environment variable 'noreplykey' not set.")
        return

    # Create the email message
    message = MIMEMultipart()
    message["From"]     = sender_email
    message["To"]       = receiver_email
    message["Subject"]  = subject
    message.attach(MIMEText(body, "plain"))

     # Flag the email as important(only for peak season)
    message["X-Priority"]           = "1"  # High priority
    message["X-MSMail-Priority"]    = "High"  # For Outlook
    message["Importance"]           = "High"  # General importance header

    # Every message must have its own message id. This id is not stored just in the message.
    # This id is an unique identifier of the message.
    message["Message-Id"] = make_msgid()

    # Connect to the SMTP server
    try:
        #with smtplib.SMTP("smtp-mail.outlook.com", 587) as server:
        with smtplib.SMTP("smtp.office365.com", 587) as server:
            server.starttls()
            server.login(sender_email, password)
            server.sendmail(sender_email, receiver_email, message.as_string())
    except smtplib.SMTPException as smtp_error:
        print(f"Error sending email: {smtp_error}")
    except Exception as e:
        print(f"An unexpected error occurred during email sending: {e}")

def format_duration(seconds):
  """
  Formats a given number of seconds into a human-readable string with hours, minutes, and seconds.

  Args:
      seconds: The number of seconds to format.

  Returns:
      A string representing the duration in hours, minutes, and seconds.
  """
  hours = seconds // 3600
  minutes = (seconds % 3600) // 60
  seconds = seconds % 60

  parts = []
  if hours > 0:
    parts.append(f"{hours} hour{'s' if hours > 1 else ''}")
  if minutes > 0:
    parts.append(f"{minutes} minute{'s' if minutes > 1 else ''}")
  if seconds > 0:
    parts.append(f"{seconds} second{'s' if seconds > 1 else ''}")

  return ", ".join(parts) if parts else "0 seconds"

# Example usage
#duration_string = format_duration(3662)
#print(duration_string)  # Output: 1 hour, 1 minute, 2 seconds

def run_query_and_save_results_to_csv():
    sql01 = 'prodsql01'
    sql02 = 'prodsql02'
    connection_replica = None
    connection_prod = None

    try:
        connection_replica = pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={sql02};DATABASE=DAX_PROD;Trusted_Connection=yes;')
    except pyodbc.Error as e:
        print(f"Error connecting to {sql02}: {e}")
        print(f"Will use {sql01} for both connections")
        sql02 = sql01

    try:
        connection_prod = pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={sql01};DATABASE=DAX_PROD;Trusted_Connection=yes;')
    except pyodbc.Error as e:
        print(f"Error connecting to {sql01}: {e}")
        print("Will try again on the next run")
        if connection_replica:
            connection_replica.close()
        return

    # If replica connection failed, use prod connection for both
    if connection_replica is None:
        connection_replica = connection_prod

    try:
        # Get the current date in YYYYMMDD format
        #current_date = datetime.datetime.today().strftime('%Y%m%d')
        
        # Create the CSV file path with the current date and "_v2" suffix
        #csv_file_path = f'{current_date}_v2.csv'

        # Using the same file to gather more information in one place
        csv_file_path = 'VoiceBlkMon_v3.csv'

        # Check if the CSV file exists
        file_exists = os.path.exists(csv_file_path)

        # Execute the SQL query on prodaxsql2  or prodaxsql
        if sql01 == sql02:
            connection_replica = connection_prod
        cursor_replica = connection_replica.cursor()
        cursor_replica.execute('''
          SELECT
            (SELECT COUNT(*) FROM HAVOICEINTEGRATIONQUEUEPUTS WHERE [STATUS] = 0 ) AS OrdersCnt
            , (SELECT COUNT(*) FROM HAVOICEINTEGRATIONQUEUEPICKS WHERE STATUS = 0) AS PicksCnt
            , CAST(GETUTCDATE() AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS DATETIME) AS KYDT
        ''')
        #results_tk = cursor_tk.fetchall()
        results_replica = cursor_replica.fetchone()

        # Execute the SQL query on prodaxsql
        cursor_prod = connection_prod.cursor()
        cursor_prod.execute('''
                SELECT TOP 1
                -- Blocked process information
                CAST(
                CASE 
                    WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
                    ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1))
                END AS INT) AS Blked_AXSessionID,
                r.session_id AS Blked_SQLSessionID,
                (SELECT COUNT(*)
                FROM sys.dm_exec_requests r_count
                WHERE r_count.blocking_session_id <> 0) AS Blked_Lines,
                CASE 
                    WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
                    ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) - 1)
                END AS Blked_AXUser,
                s.host_name AS Blked_Host,
                r.wait_time / 1000 AS Max_TimeInSec,
                r.wait_type AS Blked_WaitType,
                DB_NAME(r.database_id) AS Blked_DB,
                t.text AS Blked_SQL_Text,
                s.status AS Blked_Status,
                r.command AS Blked_Command,
                -- Blocking process information
                            
                CASE 
                    WHEN LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))) = '' THEN NULL
                    ELSE SUBSTRING(LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1))
                END AS Blking_AXSessionID,
                r2.session_id AS Blking_SQLSessionID,
                CASE 
                    WHEN LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))) = '' THEN NULL
                    ELSE SUBSTRING(LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) - 1)
                END AS Blking_AXUser,
                s2.host_name AS Blking_Host,
                --r2.wait_time / 1000 AS Blking_Max_TimeInSec,
                r2.wait_type AS Blking_WaitType,
                DB_NAME(r2.database_id) AS Blking_DB,
                t2.text AS Blking_SQL_Text,
                s2.status AS Blking_Status,
                r2.command AS Blking_Command
                --, SYSDATETIMEOFFSET() AT TIME ZONE 'Eastern Standard Time' AS KYDateTime
            FROM (
                SELECT TOP 1
                    r1.blocking_session_id AS LeadBlockerSessionID,
                    r1.session_id AS BlockedSessionID
                FROM sys.dm_exec_requests r1
                WHERE r1.blocking_session_id <> 0
                ORDER BY r1.wait_time DESC
            ) rb
            INNER JOIN sys.dm_exec_requests r ON rb.BlockedSessionID = r.session_id
            INNER JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
            CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
            INNER JOIN sys.dm_exec_requests r2 ON rb.LeadBlockerSessionID = r2.session_id
            INNER JOIN sys.dm_exec_sessions s2 ON r2.session_id = s2.session_id
            CROSS APPLY sys.dm_exec_sql_text(r2.sql_handle) t2
            ORDER BY Max_TimeInSec DESC
        ''')
        #results_prod = cursor_prod.fetchall()
        results_prod = cursor_prod.fetchone()
        csv_columns = [
            'OrdersCnt'    , 'PicksCnt'    , 'KYDT', 
            'Blked_AXSessionID','Blked_SQLSessionID', 'Blked_Lines', 
            'Blked_AXUser' , 'Blked_Host'  , 'Max_TimeInSec', 
            'Blked_WaitType', 'Blked_DB', 'Blked_SQL', 
            'Blked_Status', 'Blked_Command', 
            'Blking_AXSessionID', 'Blking_SQLSessionID', 
            'Blking_AXUser', 'Blking_Host', 'Blking_WaitType', 
            'Blking_DB', 'Blking_SQL','Blking_Status', 'Blking_Command']
        if results_prod is not None:
            combined_results = tuple(results_replica) + tuple(results_prod)
        else:
            combined_results = tuple(results_replica) + (None,) * (len(csv_columns) - 3)  # If results_prod is None, fill with None values
        # Extract relevant values for alert conditions
        blk_lines       = combined_results[csv_columns.index('Blked_Lines')     ]
        max_wait_time   = combined_results[csv_columns.index('Max_TimeInSec')   ]   
        blocked_user    = combined_results[csv_columns.index('Blked_AXUser')    ] 
        blocked_host    = combined_results[csv_columns.index('Blked_Host')      ] 
        blocking_user   = combined_results[csv_columns.index('Blking_AXUser')   ] 
        blocking_host   = combined_results[csv_columns.index('Blking_Host')     ] 
        orders_cnt      = combined_results[csv_columns.index('OrdersCnt')       ]
        picks_cnt       = combined_results[csv_columns.index('PicksCnt')        ]

        # Check conditions for email alert
        try:
            if blk_lines is not None and max_wait_time is not None:
                blk_lines_int = int(blk_lines) if blk_lines is not None else 0
                max_wait_time_int = int(max_wait_time) if max_wait_time is not None else 0
                orders_cnt_int = int(orders_cnt) if orders_cnt is not None else 0
                picks_cnt_int = int(picks_cnt) if picks_cnt is not None else 0
                
                if blk_lines_int > alert_blk_lines or max_wait_time_int > alert_wait_time or \
                    (blk_lines_int > alert_blk_lines and (orders_cnt_int > orders_treshold or picks_cnt_int > picks_treshold)):
                    subject = "Alert: Blocking Conditions Exceeded"
                    body = f"Orders pending to export: {orders_cnt_int:,} \nPicks pending to export: {picks_cnt_int:,}\nBlocked Lines: {blk_lines_int}\nLongest transaction: \nUser {blocked_user} on {blocked_host}, have been blocked by {blocking_user} on {blocking_host} for {format_duration(max_wait_time_int)}"
                    send_email_alert(subject, body)
                    
                    # Write to CSV only when alert is triggered
                    try:
                        with open(csv_file_path, 'a' if file_exists else 'w', newline='') as csvfile:
                            csv_writer = csv.writer(csvfile)
                            if not file_exists:
                                csv_writer.writerow(csv_columns)
                            csv_writer.writerow(combined_results)
                    except (IOError, OSError) as e:
                        print(f"Error writing to CSV file: {e}")
        except (ValueError, TypeError) as e:
            print(f"Error processing alert conditions: {e}")

    except Exception as e:
        print(f"An error occurred: {e}")

    finally:
        # Close the database connections
        try:
            if connection_replica and connection_replica != connection_prod:
                connection_replica.close()
        except Exception as e:
            print(f"Error closing replica connection: {e}")
        
        try:
            if connection_prod:
                connection_prod.close()
        except Exception as e:
            print(f"Error closing prod connection: {e}")

if __name__ == '__main__':
    print("Starting voice monitor...")
    try:
        while True:
            try:
                run_query_and_save_results_to_csv()
            except KeyboardInterrupt:
                print("Monitoring stopped by user")
                break
            except Exception as e:
                print(f"Error in monitoring cycle: {e}")
                print("Continuing to next cycle...")
            
            try:
                time.sleep(intCheckIntervalInSeconds)
            except KeyboardInterrupt:
                print("Monitoring stopped by user")
                break
    except Exception as e:
        print(f"Fatal error in main loop: {e}")
