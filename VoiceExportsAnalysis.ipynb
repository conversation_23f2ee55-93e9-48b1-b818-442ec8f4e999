{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Load the CSV file into a DataFrame\n", "csv_file_path = 'VoiceBlkMon_v3.csv'\n", "df = pd.read_csv(csv_file_path, usecols=[0, 1, 2], names=['OrdersCnt', 'PicksCnt', 'Timestamp'], header=0)\n", "\n", "# Convert the 'Timestamp' column to datetime\n", "#df['Timestamp'] = pd.to_datetime(df['Timestamp'], format='%Y-%m-%d %H:%M:%S.%f')\n", "df['Timestamp'] = pd.to_datetime(df['Timestamp'])\n", "\n", "\n", "# Plot the data\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(df['Timestamp'], df['OrdersCnt'], label='OrdersCnt')\n", "plt.plot(df['Timestamp'], df['PicksCnt'], label='PicksCnt')\n", "\n", "# Add labels and title\n", "plt.xlabel('Time')\n", "plt.ylabel('Values')\n", "plt.title('Behavior of OrdersCnt and PicksCnt Over Time')\n", "plt.legend()\n", "\n", "# Rotate the x-axis labels for better readability\n", "plt.xticks(rotation=45)\n", "\n", "# Show the plot\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from matplotlib.widgets import Slider\n", "\n", "# Load the CSV file into a DataFrame\n", "csv_file_path = 'VoiceBlkMon_v3.csv'\n", "df = pd.read_csv(csv_file_path, usecols=[0, 1, 2], names=['OrdersCnt', 'PicksCnt', 'Timestamp'], header=0)\n", "\n", "# Convert the 'Timestamp' column to datetime with the specified format\n", "df['Timestamp'] = pd.to_datetime(df['Timestamp'])\n", "\n", "# Create the plot\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "plt.subplots_adjust(left=0.1, bottom=0.25)\n", "\n", "# Initial plot\n", "line1, = plt.plot(df['Timestamp'], df['OrdersCnt'], label='OrdersCnt')\n", "line2, = plt.plot(df['Timestamp'], df['PicksCnt'], label='PicksCnt')\n", "\n", "# Add labels and title\n", "plt.xlabel('Time')\n", "plt.ylabel('Values')\n", "plt.title('Behavior of OrdersCnt and PicksCnt Over Time')\n", "plt.legend()\n", "\n", "# Rotate the x-axis labels for better readability\n", "plt.xticks(rotation=45)\n", "\n", "# Add sliders for year, month, and day\n", "axcolor = 'lightgoldenrodyellow'\n", "ax_year = plt.axes([0.1, 0.1, 0.65, 0.03], facecolor=axcolor)\n", "ax_month = plt.axes([0.1, 0.15, 0.65, 0.03], facecolor=axcolor)\n", "ax_day = plt.axes([0.1, 0.2, 0.65, 0.03], facecolor=axcolor)\n", "\n", "year_slider = Slider(ax_year, 'Year', df['Timestamp'].dt.year.min(), df['Timestamp'].dt.year.max(), valinit=df['Timestamp'].dt.year.min(), valstep=1)\n", "month_slider = Slider(ax_month, 'Month', 1, 12, valinit=1, valstep=1)\n", "day_slider = Slider(ax_day, 'Day', 1, 31, valinit=1, valstep=1)\n", "\n", "# Update function for sliders\n", "def update(val):\n", "    year = int(year_slider.val)\n", "    month = int(month_slider.val)\n", "    day = int(day_slider.val)\n", "    \n", "    filtered_df = df[(df['Timestamp'].dt.year == year) & (df['Timestamp'].dt.month == month) & (df['Timestamp'].dt.day == day)]\n", "    \n", "    line1.set_data(filtered_df['Timestamp'], filtered_df['OrdersCnt'])\n", "    line2.set_data(filtered_df['Timestamp'], filtered_df['PicksCnt'])\n", "    \n", "    ax.relim()\n", "    ax.autoscale_view()\n", "    plt.draw()\n", "\n", "# Attach the update function to the sliders\n", "year_slider.on_changed(update)\n", "month_slider.on_changed(update)\n", "day_slider.on_changed(update)\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8c0eb941fb6f4a85ae2f8128909b7738", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Dropdown(description='Year:', options=(np.int32(2024), np.int32(2025)), value=np.int32(2…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "import calendar\n", "from matplotlib.dates import DateFormatter\n", "\n", "# Load the CSV file into a DataFrame\n", "csv_file_path = 'VoiceBlkMon_v3.csv'\n", "df = pd.read_csv(csv_file_path, usecols=[0, 1, 2], names=['OrdersCnt', 'PicksCnt', 'Timestamp'], header=0)\n", "\n", "# Convert the 'Timestamp' column to datetime with the specified format\n", "df['Timestamp'] = pd.to_datetime(df['Timestamp'])\n", "\n", "# Create dropdown widgets for year, month, and day\n", "year_options = df['Timestamp'].dt.year.unique()\n", "month_options = range(1, 13)\n", "day_options = range(1, 32)\n", "\n", "year_dropdown = widgets.Dropdown(options=year_options, description='Year:')\n", "month_dropdown = widgets.Dropdown(options=month_options, description='Month:')\n", "day_dropdown = widgets.Dropdown(options=day_options, description='Day:')\n", "\n", "# Function to dynamically update day options\n", "def update_day_options(*args):\n", "    year = year_dropdown.value\n", "    month = month_dropdown.value\n", "    if year and month:\n", "        valid_days = range(1, calendar.monthrange(year, month)[1] + 1)  # Get valid days for the month\n", "        day_dropdown.options = valid_days\n", "\n", "# Observe changes in year and month dropdowns\n", "year_dropdown.observe(update_day_options, names='value')\n", "month_dropdown.observe(update_day_options, names='value')\n", "\n", "# Function to update the plot\n", "def update_plot(year, month, day):\n", "    filtered_df = df[(df['Timestamp'].dt.year == year) & \n", "                     (df['Timestamp'].dt.month == month) & \n", "                     (df['Timestamp'].dt.day == day)]\n", "    \n", "    if filtered_df.empty:\n", "        print(f\"No data available for {year}-{month:02d}-{day:02d}.\")\n", "        return\n", "    \n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(filtered_df['Timestamp'], filtered_df['OrdersCnt'], label='Pending Orders', marker='o')\n", "    plt.plot(filtered_df['Timestamp'], filtered_df['PicksCnt'], label='Pending Picks', marker='x')\n", "    \n", "    plt.xlabel('Time')\n", "    plt.ylabel('Values')\n", "    plt.title('Behavior of Orders and Picks Exports Over Time')\n", "    plt.legend()\n", "    plt.grid(True, linestyle='--', alpha=0.7)\n", "    \n", "    # Format x-axis as hours/minutes\n", "    date_format = DateFormatter('%H:%M:%S')\n", "    plt.gca().xaxis.set_major_formatter(date_format)\n", "    \n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Create an interactive output\n", "interactive_plot = widgets.interactive(update_plot, year=year_dropdown, month=month_dropdown, day=day_dropdown)\n", "display(interactive_plot)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9f3689b4fcf64419a6100985dd543d40", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Dropdown(description='Year:', options=(np.int32(2024), np.int32(2025)), value=np.int32(2…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load the CSV file into a DataFrame\n", "csv_file_path = 'VoiceBlkMon_v3.csv'\n", "df = pd.read_csv(csv_file_path, usecols=[2, 5, 8], names=['Timestamp', 'BlkedLines', 'MaxTimeInSeconds'], header=0)\n", "\n", "# Convert the 'Timestamp' column to datetime with the specified format\n", "df['Timestamp'] = pd.to_datetime(df['Timestamp'])\n", "\n", "# Create dropdown widgets for year, month, and day\n", "year_options = df['Timestamp'].dt.year.unique()\n", "month_options = range(1, 13)\n", "day_options = range(1, 32)\n", "\n", "year_dropdown = widgets.Dropdown(options=year_options, description='Year:')\n", "month_dropdown = widgets.Dropdown(options=month_options, description='Month:')\n", "day_dropdown = widgets.Dropdown(options=day_options, description='Day:')\n", "\n", "# Function to dynamically update day options\n", "def update_day_options(*args):\n", "    year = year_dropdown.value\n", "    month = month_dropdown.value\n", "    if year and month:\n", "        valid_days = range(1, calendar.monthrange(year, month)[1] + 1)  # Get valid days for the month\n", "        day_dropdown.options = valid_days\n", "\n", "# Observe changes in year and month dropdowns\n", "year_dropdown.observe(update_day_options, names='value')\n", "month_dropdown.observe(update_day_options, names='value')\n", "\n", "# Function to update the plot\n", "def update_plot(year, month, day):\n", "    filtered_df = df[(df['Timestamp'].dt.year == year) & \n", "                     (df['Timestamp'].dt.month == month) & \n", "                     (df['Timestamp'].dt.day == day)]\n", "    \n", "    if filtered_df.empty:\n", "        print(f\"No data available for {year}-{month:02d}-{day:02d}.\")\n", "        return\n", "    \n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(filtered_df['Timestamp'], filtered_df['BlkedLines'], label='Blocked Lines', marker='o')\n", "    plt.plot(filtered_df['Timestamp'], filtered_df['MaxTimeInSeconds'], label='Max Time In Seconds', marker='x')\n", "    \n", "    plt.xlabel('Time')\n", "    plt.ylabel('Values')\n", "    plt.title('Behavior of Blocked Processes and Waiting Time')\n", "    plt.legend()\n", "    plt.grid(True, linestyle='--', alpha=0.7)\n", "    \n", "    # Format x-axis as hours/minutes\n", "    date_format = DateFormatter('%H:%M:%S')\n", "    plt.gca().xaxis.set_major_formatter(date_format)\n", "    \n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Create an interactive output\n", "interactive_plot = widgets.interactive(update_plot, year=year_dropdown, month=month_dropdown, day=day_dropdown)\n", "display(interactive_plot)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}