import matplotlib.pyplot as plt
import pandas as pd

# Load the CSV file into a DataFrame
csv_file_path = 'VoiceBlkMon_v3.csv'
df = pd.read_csv(csv_file_path, usecols=[0, 1, 2], names=['OrdersCnt', 'PicksCnt', 'Timestamp'], header=0)

# Convert the 'Timestamp' column to datetime
#df['Timestamp'] = pd.to_datetime(df['Timestamp'], format='%Y-%m-%d %H:%M:%S.%f')
df['Timestamp'] = pd.to_datetime(df['Timestamp'])


# Plot the data
plt.figure(figsize=(10, 6))
plt.plot(df['Timestamp'], df['OrdersCnt'], label='OrdersCnt')
plt.plot(df['Timestamp'], df['PicksCnt'], label='PicksCnt')

# Add labels and title
plt.xlabel('Time')
plt.ylabel('Values')
plt.title('Behavior of OrdersCnt and PicksCnt Over Time')
plt.legend()

# Rotate the x-axis labels for better readability
plt.xticks(rotation=45)

# Show the plot
plt.tight_layout()
plt.show()

import matplotlib.pyplot as plt
import pandas as pd
from matplotlib.widgets import Slider

# Load the CSV file into a DataFrame
csv_file_path = 'VoiceBlkMon_v3.csv'
df = pd.read_csv(csv_file_path, usecols=[0, 1, 2], names=['OrdersCnt', 'PicksCnt', 'Timestamp'], header=0)

# Convert the 'Timestamp' column to datetime with the specified format
df['Timestamp'] = pd.to_datetime(df['Timestamp'])

# Create the plot
fig, ax = plt.subplots(figsize=(10, 6))
plt.subplots_adjust(left=0.1, bottom=0.25)

# Initial plot
line1, = plt.plot(df['Timestamp'], df['OrdersCnt'], label='OrdersCnt')
line2, = plt.plot(df['Timestamp'], df['PicksCnt'], label='PicksCnt')

# Add labels and title
plt.xlabel('Time')
plt.ylabel('Values')
plt.title('Behavior of OrdersCnt and PicksCnt Over Time')
plt.legend()

# Rotate the x-axis labels for better readability
plt.xticks(rotation=45)

# Add sliders for year, month, and day
axcolor = 'lightgoldenrodyellow'
ax_year = plt.axes([0.1, 0.1, 0.65, 0.03], facecolor=axcolor)
ax_month = plt.axes([0.1, 0.15, 0.65, 0.03], facecolor=axcolor)
ax_day = plt.axes([0.1, 0.2, 0.65, 0.03], facecolor=axcolor)

year_slider = Slider(ax_year, 'Year', df['Timestamp'].dt.year.min(), df['Timestamp'].dt.year.max(), valinit=df['Timestamp'].dt.year.min(), valstep=1)
month_slider = Slider(ax_month, 'Month', 1, 12, valinit=1, valstep=1)
day_slider = Slider(ax_day, 'Day', 1, 31, valinit=1, valstep=1)

# Update function for sliders
def update(val):
    year = int(year_slider.val)
    month = int(month_slider.val)
    day = int(day_slider.val)
    
    filtered_df = df[(df['Timestamp'].dt.year == year) & (df['Timestamp'].dt.month == month) & (df['Timestamp'].dt.day == day)]
    
    line1.set_data(filtered_df['Timestamp'], filtered_df['OrdersCnt'])
    line2.set_data(filtered_df['Timestamp'], filtered_df['PicksCnt'])
    
    ax.relim()
    ax.autoscale_view()
    plt.draw()

# Attach the update function to the sliders
year_slider.on_changed(update)
month_slider.on_changed(update)
day_slider.on_changed(update)

# Show the plot
plt.show()

import matplotlib.pyplot as plt
import pandas as pd
import ipywidgets as widgets
from IPython.display import display
import calendar
from matplotlib.dates import DateFormatter

# Load the CSV file into a DataFrame
csv_file_path = 'VoiceBlkMon_v3.csv'
df = pd.read_csv(csv_file_path, usecols=[0, 1, 2], names=['OrdersCnt', 'PicksCnt', 'Timestamp'], header=0)

# Convert the 'Timestamp' column to datetime with the specified format
df['Timestamp'] = pd.to_datetime(df['Timestamp'])

# Create dropdown widgets for year, month, and day
year_options = df['Timestamp'].dt.year.unique()
month_options = range(1, 13)
day_options = range(1, 32)

year_dropdown = widgets.Dropdown(options=year_options, description='Year:')
month_dropdown = widgets.Dropdown(options=month_options, description='Month:')
day_dropdown = widgets.Dropdown(options=day_options, description='Day:')

# Function to dynamically update day options
def update_day_options(*args):
    year = year_dropdown.value
    month = month_dropdown.value
    if year and month:
        valid_days = range(1, calendar.monthrange(year, month)[1] + 1)  # Get valid days for the month
        day_dropdown.options = valid_days

# Observe changes in year and month dropdowns
year_dropdown.observe(update_day_options, names='value')
month_dropdown.observe(update_day_options, names='value')

# Function to update the plot
def update_plot(year, month, day):
    filtered_df = df[(df['Timestamp'].dt.year == year) & 
                     (df['Timestamp'].dt.month == month) & 
                     (df['Timestamp'].dt.day == day)]
    
    if filtered_df.empty:
        print(f"No data available for {year}-{month:02d}-{day:02d}.")
        return
    
    plt.figure(figsize=(10, 6))
    plt.plot(filtered_df['Timestamp'], filtered_df['OrdersCnt'], label='Pending Orders', marker='o')
    plt.plot(filtered_df['Timestamp'], filtered_df['PicksCnt'], label='Pending Picks', marker='x')
    
    plt.xlabel('Time')
    plt.ylabel('Values')
    plt.title('Behavior of Orders and Picks Exports Over Time')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # Format x-axis as hours/minutes
    date_format = DateFormatter('%H:%M:%S')
    plt.gca().xaxis.set_major_formatter(date_format)
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

# Create an interactive output
interactive_plot = widgets.interactive(update_plot, year=year_dropdown, month=month_dropdown, day=day_dropdown)
display(interactive_plot)

# Load the CSV file into a DataFrame
csv_file_path = 'VoiceBlkMon_v3.csv'
df = pd.read_csv(csv_file_path, usecols=[2, 5, 8], names=['Timestamp', 'BlkedLines', 'MaxTimeInSeconds'], header=0)

# Convert the 'Timestamp' column to datetime with the specified format
df['Timestamp'] = pd.to_datetime(df['Timestamp'])

# Create dropdown widgets for year, month, and day
year_options = df['Timestamp'].dt.year.unique()
month_options = range(1, 13)
day_options = range(1, 32)

year_dropdown = widgets.Dropdown(options=year_options, description='Year:')
month_dropdown = widgets.Dropdown(options=month_options, description='Month:')
day_dropdown = widgets.Dropdown(options=day_options, description='Day:')

# Function to dynamically update day options
def update_day_options(*args):
    year = year_dropdown.value
    month = month_dropdown.value
    if year and month:
        valid_days = range(1, calendar.monthrange(year, month)[1] + 1)  # Get valid days for the month
        day_dropdown.options = valid_days

# Observe changes in year and month dropdowns
year_dropdown.observe(update_day_options, names='value')
month_dropdown.observe(update_day_options, names='value')

# Function to update the plot
def update_plot(year, month, day):
    filtered_df = df[(df['Timestamp'].dt.year == year) & 
                     (df['Timestamp'].dt.month == month) & 
                     (df['Timestamp'].dt.day == day)]
    
    if filtered_df.empty:
        print(f"No data available for {year}-{month:02d}-{day:02d}.")
        return
    
    plt.figure(figsize=(10, 6))
    plt.plot(filtered_df['Timestamp'], filtered_df['BlkedLines'], label='Blocked Lines', marker='o')
    plt.plot(filtered_df['Timestamp'], filtered_df['MaxTimeInSeconds'], label='Max Time In Seconds', marker='x')
    
    plt.xlabel('Time')
    plt.ylabel('Values')
    plt.title('Behavior of Blocked Processes and Waiting Time')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # Format x-axis as hours/minutes
    date_format = DateFormatter('%H:%M:%S')
    plt.gca().xaxis.set_major_formatter(date_format)
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

# Create an interactive output
interactive_plot = widgets.interactive(update_plot, year=year_dropdown, month=month_dropdown, day=day_dropdown)
display(interactive_plot)