#!/usr/bin/env python3
"""
Voice Monitor v4 - Enhanced SQL Server Blocking Monitor

This script monitors SQL Server blocking conditions and voice integration queue status.
It captures comprehensive blocking snapshots including all victims and lead blockers,
classifies queries (bear/tiger/lion), and writes detailed forensic data to CSV.

Key improvements over v3:
- Captures all blocked victims per snapshot (not just the worst one)
- Identifies true lead blockers and chain depth
- Handles idle/sleeping blockers robustly
- Classifies costly queries using text landmarks and query hashes
- Provides lock resource and mode details
- Writes pandas/polars-friendly CSV with consistent schema

Author: Generated by Augment Agent
Version: 4.0.0
"""

import pyodbc
import csv
import os
import time
import smtplib
import hashlib
import uuid
from datetime import datetime, timezone
from typing import Optional, List, Dict, Tuple, Any
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.utils import make_msgid

# Configuration Constants
ALERT_BLOCKED_LINES_THRESHOLD = 3      # Alert if more than X lines are blocked
ALERT_WAIT_TIME_THRESHOLD = 30         # Alert if wait time exceeds X seconds
ORDERS_THRESHOLD = 50                  # Alert if more than X orders pending
PICKS_THRESHOLD = 250                  # Alert if more than X picks pending
CHECK_INTERVAL_SECONDS = 60            # Check every X seconds
SQL_TEXT_TRUNCATE_LENGTH = 1000        # Truncate SQL text to X characters
SCRIPT_VERSION = "4.0.0"
CSV_VERSION = "v4"
CLASSIFIER_VERSION = "v1"

# Server Configuration
SQL_PRIMARY = 'prodsql01'
SQL_REPLICA = 'prodsql02'
DATABASE_NAME = 'DAX_PROD'
CSV_FILE_PATH = 'VoiceBlkMon_v4.csv'

# Email Configuration
EMAIL_DOMAIN = 'hannaandersson.com'
EMAIL_SENDER = 'no-reply'
EMAIL_RECEIVER = 'labreu'


class QueryClassifier:
    """
    Classifies SQL queries as bear/tiger/lion based on text landmarks and learned hashes.
    
    Uses a two-stage approach:
    1. Text-based landmark matching (normalized SQL)
    2. Query hash learning for long-term accuracy
    """
    
    def __init__(self):
        self.known_hashes: Dict[str, set] = {
            'bear': set(),
            'tiger': set(), 
            'lion': set()
        }
        
    def normalize_sql(self, sql_text: str) -> str:
        """Normalize SQL text for consistent matching."""
        if not sql_text:
            return ""
        
        # Convert to lowercase and collapse whitespace
        normalized = ' '.join(sql_text.lower().split())
        # Remove common bracket variations and extra punctuation
        normalized = normalized.replace('[', '').replace(']', '')
        return normalized
    
    def classify_bear(self, sql_text: str) -> bool:
        """
        Classify 'bear' query - large inventory aggregation SELECT.
        
        Landmarks:
        - FROM INVENTSUMDELTADIM + INVENTSUM + INVENTDIM
        - Multiple SUM(T2.*) aggregates
        - GROUP BY T1.* (many dimension columns)
        - Join predicates on ITEMID and INVENTDIMID
        """
        normalized = self.normalize_sql(sql_text)
        
        required_landmarks = [
            'from inventsumdeltadim',
            'inventsum',
            'inventdim',
            'sum(t2.',
            'group by t1.',
            't2.inventdimid=t3.inventdimid',
            't2.itemid=t1.itemid'
        ]
        
        return all(landmark in normalized for landmark in required_landmarks)
    
    def classify_tiger(self, sql_text: str) -> bool:
        """
        Classify 'tiger' query - INVENTSUM SELECT with UPDLOCK.
        
        Landmarks:
        - FROM INVENTSUM WITH (UPDLOCK)
        - JOIN INVENTSUMDELTADIM
        - Join predicates on ITEMID and INVENTDIMID
        """
        normalized = self.normalize_sql(sql_text)
        
        required_landmarks = [
            'from inventsum',
            'with (updlock',
            'inventsumdeltadim',
            't2.itemid=t1.itemid',
            't2.inventdimid=t1.inventdimid'
        ]
        
        return all(landmark in normalized for landmark in required_landmarks)
    
    def classify_lion(self, sql_text: str) -> bool:
        """
        Classify 'lion' query - INVENTSUM UPDATE with delta aggregation.
        
        Landmarks:
        - UPDATE INVENTSUM SET
        - Subquery with SUM aggregates from INVENTSUMDELTA
        - GROUP BY INVENTSUMDELTA.ITEMID, INVENTSUMDELTA.INVENTDIMID
        - Join back on ITEMID and INVENTDIMID
        """
        normalized = self.normalize_sql(sql_text)
        
        required_landmarks = [
            'update inventsum set',
            'from (select sum(',
            'from inventsumdelta',
            'group by inventsumdelta.itemid, inventsumdelta.inventdimid',
            'inventsum.itemid = inventsumdelta.itemid',
            'inventsum.inventdimid = inventsumdelta.inventdimid'
        ]
        
        return all(landmark in normalized for landmark in required_landmarks)
    
    def classify_query(self, sql_text: str, sql_hash: Optional[str] = None) -> Tuple[str, str]:
        """
        Classify a query and return (label, matched_tokens).
        
        Returns:
            Tuple of (query_label, matched_signature_tokens)
        """
        if not sql_text:
            return 'unknown', ''
        
        # First check known hashes for fast classification
        if sql_hash:
            for label, hashes in self.known_hashes.items():
                if sql_hash in hashes:
                    return label, f'hash_{label}'
        
        # Text-based classification
        if self.classify_bear(sql_text):
            if sql_hash:
                self.known_hashes['bear'].add(sql_hash)
            return 'bear', 'inventsumdeltadim;inventsum;inventdim;sum_agg;group_by'
        
        if self.classify_tiger(sql_text):
            if sql_hash:
                self.known_hashes['tiger'].add(sql_hash)
            return 'tiger', 'inventsum;updlock;inventsumdeltadim;itemid_join'
        
        if self.classify_lion(sql_text):
            if sql_hash:
                self.known_hashes['lion'].add(sql_hash)
            return 'lion', 'update_inventsum;delta_agg;inventsumdelta;itemid_join'
        
        return 'unknown', ''


def compute_sql_hash(sql_text: str) -> str:
    """Compute a stable hash of SQL text for query identification."""
    if not sql_text:
        return ''
    
    # Normalize for hashing (remove extra whitespace, case-insensitive)
    normalized = ' '.join(sql_text.strip().split())
    return hashlib.sha1(normalized.encode('utf-8')).hexdigest()[:16]


def parse_ax_context_info(context_info: Optional[bytes]) -> Tuple[Optional[str], Optional[int]]:
    """
    Parse AX user and session ID from SQL Server context_info.
    
    Expected format: "username sessionid ..."
    
    Returns:
        Tuple of (ax_user, ax_session_id)
    """
    if not context_info:
        return None, None
    
    try:
        context_str = context_info.decode('utf-8', errors='ignore').strip()
        if not context_str:
            return None, None
        
        parts = context_str.split()
        if len(parts) >= 2:
            ax_user = parts[0] if parts[0] else None
            try:
                ax_session_id = int(parts[1]) if parts[1] else None
            except ValueError:
                ax_session_id = None
            return ax_user, ax_session_id
    except Exception:
        pass
    
    return None, None


def format_duration(seconds: int) -> str:
    """Format seconds into human-readable duration string."""
    if seconds <= 0:
        return "0 seconds"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    remaining_seconds = seconds % 60
    
    parts = []
    if hours > 0:
        parts.append(f"{hours} hour{'s' if hours > 1 else ''}")
    if minutes > 0:
        parts.append(f"{minutes} minute{'s' if minutes > 1 else ''}")
    if remaining_seconds > 0:
        parts.append(f"{remaining_seconds} second{'s' if remaining_seconds > 1 else ''}")
    
    return ", ".join(parts)


def send_email_alert(subject: str, body: str) -> None:
    """Send email alert using configured SMTP settings."""
    sender_email = f"{EMAIL_SENDER}@{EMAIL_DOMAIN}"
    receiver_email = f"{EMAIL_RECEIVER}@{EMAIL_DOMAIN}"
    
    # Get email password from environment variable
    password = os.environ.get("noreplykey")
    if not password:
        print("Error: Environment variable 'noreplykey' not set.")
        return
    
    try:
        # Create email message
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = receiver_email
        message["Subject"] = subject
        message["Message-Id"] = make_msgid()
        message.attach(MIMEText(body, "plain"))
        
        # Send email
        with smtplib.SMTP("smtp.office365.com", 587) as server:
            server.starttls()
            server.login(sender_email, password)
            server.sendmail(sender_email, receiver_email, message.as_string())
            
    except smtplib.SMTPException as smtp_error:
        print(f"Error sending email: {smtp_error}")
    except Exception as e:
        print(f"Unexpected error during email sending: {e}")


class BlockingSnapshot:
    """Represents a complete blocking snapshot with all victims and lead blocker details."""
    
    def __init__(self):
        self.snapshot_id = str(uuid.uuid4())
        self.timestamp_utc = datetime.now(timezone.utc)
        self.timestamp_local = None
        self.server_primary = SQL_PRIMARY
        self.server_replica = SQL_REPLICA
        self.orders_cnt = 0
        self.picks_cnt = 0
        self.blocked_lines_global = 0
        self.max_wait_time_sec_global = 0
        self.victims: List[Dict[str, Any]] = []


def get_voice_queue_counts(connection: pyodbc.Connection) -> Tuple[int, int, datetime]:
    """
    Get voice integration queue counts from replica server.

    Returns:
        Tuple of (orders_count, picks_count, timestamp)
    """
    cursor = connection.cursor()
    cursor.execute('''
        SELECT
            (SELECT COUNT(*) FROM HAVOICEINTEGRATIONQUEUEPUTS WITH (NOLOCK) WHERE [STATUS] = 0) AS OrdersCnt,
            (SELECT COUNT(*) FROM HAVOICEINTEGRATIONQUEUEPICKS WITH (NOLOCK) WHERE STATUS = 0) AS PicksCnt,
            CAST(GETUTCDATE() AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS DATETIME) AS LocalTime
    ''')

    result = cursor.fetchone()
    return result[0], result[1], result[2]


def get_comprehensive_blocking_snapshot(connection: pyodbc.Connection, classifier: QueryClassifier) -> BlockingSnapshot:
    """
    Capture a comprehensive blocking snapshot including all victims and lead blockers.

    This query addresses limitations in the v3 approach:
    1. Captures ALL blocked victims (not just the worst one)
    2. Identifies true lead blockers (root of blocking chains)
    3. Handles idle/sleeping blockers robustly using LEFT JOINs
    4. Includes lock resource and mode details
    5. Computes chain depth for each victim

    Args:
        connection: SQL Server connection to primary server
        classifier: Query classifier for bear/tiger/lion detection

    Returns:
        BlockingSnapshot with all victims and lead blocker details
    """
    snapshot = BlockingSnapshot()
    cursor = connection.cursor()

    # Complex query to capture complete blocking picture
    # This replaces the simple TOP 1 approach from v3 with comprehensive chain analysis
    cursor.execute('''
        WITH BlockingChains AS (
            -- Step 1: Find all currently blocked requests
            -- sys.dm_exec_requests contains active requests that are waiting
            SELECT
                r.session_id as victim_session_id,
                r.blocking_session_id as immediate_blocker_session_id,
                r.wait_time,
                r.wait_type,
                r.wait_resource,
                r.command as victim_command,
                r.status as victim_status,
                r.database_id as victim_database_id,
                r.sql_handle as victim_sql_handle
            FROM sys.dm_exec_requests r
            WHERE r.blocking_session_id <> 0  -- Only blocked requests
        ),

        LeadBlockers AS (
            -- Step 2: Identify lead blockers (sessions that block others but are not themselves blocked)
            -- A lead blocker is a session that appears as a blocker but is not itself blocked
            SELECT DISTINCT
                bc.immediate_blocker_session_id as lead_blocker_session_id
            FROM BlockingChains bc
            WHERE bc.immediate_blocker_session_id NOT IN (
                SELECT victim_session_id FROM BlockingChains
            )
        ),

        ChainDepth AS (
            -- Step 3: Compute chain depth using recursive CTE
            -- This traces from each victim back to its lead blocker
            SELECT
                victim_session_id,
                immediate_blocker_session_id,
                1 as chain_depth,
                immediate_blocker_session_id as lead_blocker_session_id
            FROM BlockingChains bc
            WHERE bc.immediate_blocker_session_id IN (SELECT lead_blocker_session_id FROM LeadBlockers)

            UNION ALL

            SELECT
                bc.victim_session_id,
                bc.immediate_blocker_session_id,
                cd.chain_depth + 1,
                cd.lead_blocker_session_id
            FROM BlockingChains bc
            INNER JOIN ChainDepth cd ON bc.immediate_blocker_session_id = cd.victim_session_id
        )

        -- Step 4: Assemble final result with all victim and blocker details
        SELECT
            -- Global blocking metrics
            (SELECT COUNT(*) FROM sys.dm_exec_requests WHERE blocking_session_id <> 0) as blocked_lines_global,
            (SELECT MAX(wait_time)/1000 FROM sys.dm_exec_requests WHERE blocking_session_id <> 0) as max_wait_time_sec_global,

            -- Lead blocker information
            -- Use LEFT JOIN to handle idle blockers (not in dm_exec_requests but holding locks)
            lb.lead_blocker_session_id,
            s_blocker.login_name as lead_blocker_login,
            s_blocker.host_name as lead_blocker_host,
            s_blocker.program_name as lead_blocker_program,
            DB_NAME(ISNULL(r_blocker.database_id, s_blocker.database_id)) as lead_blocker_db,
            ISNULL(r_blocker.status, 'sleeping') as lead_blocker_status,
            ISNULL(r_blocker.command, 'AWAITING COMMAND') as lead_blocker_command,
            s_blocker.context_info as lead_blocker_context_info,

            -- Lead blocker SQL (handle idle blockers)
            ISNULL(t_blocker.text,
                   CASE WHEN c_blocker.most_recent_sql_handle IS NOT NULL
                        THEN (SELECT text FROM sys.dm_exec_sql_text(c_blocker.most_recent_sql_handle))
                        ELSE 'No SQL available (idle session)'
                   END) as lead_blocker_sql_text,

            -- Victim information
            cd.victim_session_id,
            cd.immediate_blocker_session_id as victim_blocking_session_id,
            cd.chain_depth,
            s_victim.login_name as victim_login,
            s_victim.host_name as victim_host,
            s_victim.program_name as victim_program,
            DB_NAME(bc.victim_database_id) as victim_db,
            bc.wait_time / 1000 as victim_wait_time_sec,
            bc.wait_type as victim_wait_type,
            bc.wait_resource as victim_wait_resource,
            bc.victim_status,
            bc.victim_command,
            s_victim.context_info as victim_context_info,
            t_victim.text as victim_sql_text,

            -- Lock details (if available)
            tl.resource_type as lock_resource_type,
            tl.request_mode as lock_mode

        FROM ChainDepth cd
        INNER JOIN BlockingChains bc ON cd.victim_session_id = bc.victim_session_id
        INNER JOIN LeadBlockers lb ON cd.lead_blocker_session_id = lb.lead_blocker_session_id

        -- Victim session and request details
        INNER JOIN sys.dm_exec_sessions s_victim ON cd.victim_session_id = s_victim.session_id
        CROSS APPLY sys.dm_exec_sql_text(bc.victim_sql_handle) t_victim

        -- Lead blocker session details (always available)
        INNER JOIN sys.dm_exec_sessions s_blocker ON lb.lead_blocker_session_id = s_blocker.session_id

        -- Lead blocker request details (LEFT JOIN for idle blockers)
        LEFT JOIN sys.dm_exec_requests r_blocker ON lb.lead_blocker_session_id = r_blocker.session_id

        -- Lead blocker SQL text (LEFT JOIN for idle blockers)
        LEFT JOIN sys.dm_exec_connections c_blocker ON lb.lead_blocker_session_id = c_blocker.session_id
        OUTER APPLY sys.dm_exec_sql_text(r_blocker.sql_handle) t_blocker

        -- Lock information (optional, may not always be available)
        LEFT JOIN sys.dm_tran_locks tl ON cd.victim_session_id = tl.request_session_id
            AND tl.request_status = 'WAIT'

        ORDER BY cd.lead_blocker_session_id, cd.chain_depth, bc.wait_time DESC
    ''')

    results = cursor.fetchall()

    if not results:
        return snapshot

    # Process results and populate snapshot
    for row in results:
        # Extract global metrics from first row
        if not snapshot.victims:
            snapshot.blocked_lines_global = row[0] or 0
            snapshot.max_wait_time_sec_global = row[1] or 0

        # Parse AX context info
        lead_blocker_ax_user, lead_blocker_ax_session = parse_ax_context_info(row[8])
        victim_ax_user, victim_ax_session = parse_ax_context_info(row[20])

        # Get SQL text and compute hashes
        lead_blocker_sql = (row[9] or '')[:SQL_TEXT_TRUNCATE_LENGTH]
        victim_sql = (row[21] or '')[:SQL_TEXT_TRUNCATE_LENGTH]

        lead_blocker_hash = compute_sql_hash(row[9] or '')
        victim_hash = compute_sql_hash(row[21] or '')

        # Classify queries
        lead_blocker_label, lead_blocker_tokens = classifier.classify_query(row[9] or '', lead_blocker_hash)
        victim_label, victim_tokens = classifier.classify_query(row[21] or '', victim_hash)

        # Determine overall query label (lead blocker takes precedence)
        if lead_blocker_label != 'unknown':
            query_label = lead_blocker_label
            query_label_source = 'blocker'
            matched_tokens = lead_blocker_tokens
        elif victim_label != 'unknown':
            query_label = victim_label
            query_label_source = 'victim'
            matched_tokens = victim_tokens
        else:
            query_label = 'unknown'
            query_label_source = ''
            matched_tokens = ''

        # Check for UPDLOCK usage
        blocker_has_updlock = 'with (updlock' in (row[9] or '').lower()
        victim_has_updlock = 'with (updlock' in (row[21] or '').lower()

        # Build victim record
        victim_record = {
            # Lead blocker context (same for all victims in this chain)
            'lead_blocker_session_id': row[2],
            'lead_blocker_login': row[3],
            'lead_blocker_host': row[4],
            'lead_blocker_program': row[5],
            'lead_blocker_db': row[6],
            'lead_blocker_status': row[7],
            'lead_blocker_command': row[8],
            'lead_blocker_sql_text': lead_blocker_sql,
            'lead_blocker_sql_hash': lead_blocker_hash,
            'lead_blocker_ax_user': lead_blocker_ax_user,
            'lead_blocker_ax_session_id': lead_blocker_ax_session,

            # Victim-specific details
            'victim_session_id': row[10],
            'victim_blocking_session_id': row[11],
            'chain_depth': row[12],
            'victim_login': row[13],
            'victim_host': row[14],
            'victim_program': row[15],
            'victim_db': row[16],
            'victim_wait_time_sec': row[17] or 0,
            'victim_wait_type': row[18],
            'victim_wait_resource': row[19],
            'victim_status': row[20],
            'victim_command': row[21],
            'victim_sql_text': victim_sql,
            'victim_sql_hash': victim_hash,
            'victim_ax_user': victim_ax_user,
            'victim_ax_session_id': victim_ax_session,

            # Lock details
            'lock_resource_type': row[22],
            'lock_mode': row[23],

            # Classification
            'query_label': query_label,
            'query_label_source': query_label_source,
            'matched_signature_tokens': matched_tokens,
            'blocker_has_updlock': blocker_has_updlock,
            'victim_has_updlock': victim_has_updlock,
        }

        snapshot.victims.append(victim_record)

    return snapshot


def write_snapshot_to_csv(snapshot: BlockingSnapshot, file_path: str) -> None:
    """
    Write blocking snapshot to CSV file with pandas/polars-friendly schema.

    Creates one row per victim with complete lead blocker context.
    Handles file creation and header writing automatically.
    """
    # Define CSV column order for consistent schema
    csv_columns = [
        'snapshot_id', 'timestamp_utc', 'timestamp_local_est',
        'server_primary', 'server_replica', 'orders_cnt', 'picks_cnt',
        'blocked_lines_global', 'max_wait_time_sec_global',

        # Lead blocker details
        'lead_blocker_session_id', 'lead_blocker_login', 'lead_blocker_host',
        'lead_blocker_program', 'lead_blocker_db', 'lead_blocker_status',
        'lead_blocker_command', 'lead_blocker_sql_text', 'lead_blocker_sql_hash',
        'lead_blocker_ax_user', 'lead_blocker_ax_session_id',

        # Victim details
        'victim_session_id', 'victim_blocking_session_id', 'chain_depth',
        'victim_login', 'victim_host', 'victim_program', 'victim_db',
        'victim_wait_time_sec', 'victim_wait_type', 'victim_wait_resource',
        'victim_status', 'victim_command', 'victim_sql_text', 'victim_sql_hash',
        'victim_ax_user', 'victim_ax_session_id',

        # Lock and classification details
        'lock_resource_type', 'lock_mode', 'query_label', 'query_label_source',
        'matched_signature_tokens', 'blocker_has_updlock', 'victim_has_updlock',

        # Metadata
        'csv_version', 'script_version', 'classifier_version'
    ]

    # Check if file exists to determine if we need headers
    file_exists = os.path.exists(file_path)

    try:
        with open(file_path, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # Write header if new file
            if not file_exists:
                writer.writerow(csv_columns)

            # Write one row per victim
            for victim in snapshot.victims:
                row_data = [
                    snapshot.snapshot_id,
                    snapshot.timestamp_utc.isoformat(),
                    snapshot.timestamp_local.isoformat() if snapshot.timestamp_local else '',
                    snapshot.server_primary,
                    snapshot.server_replica,
                    snapshot.orders_cnt,
                    snapshot.picks_cnt,
                    snapshot.blocked_lines_global,
                    snapshot.max_wait_time_sec_global,

                    # Lead blocker
                    victim['lead_blocker_session_id'],
                    victim['lead_blocker_login'],
                    victim['lead_blocker_host'],
                    victim['lead_blocker_program'],
                    victim['lead_blocker_db'],
                    victim['lead_blocker_status'],
                    victim['lead_blocker_command'],
                    victim['lead_blocker_sql_text'],
                    victim['lead_blocker_sql_hash'],
                    victim['lead_blocker_ax_user'],
                    victim['lead_blocker_ax_session_id'],

                    # Victim
                    victim['victim_session_id'],
                    victim['victim_blocking_session_id'],
                    victim['chain_depth'],
                    victim['victim_login'],
                    victim['victim_host'],
                    victim['victim_program'],
                    victim['victim_db'],
                    victim['victim_wait_time_sec'],
                    victim['victim_wait_type'],
                    victim['victim_wait_resource'],
                    victim['victim_status'],
                    victim['victim_command'],
                    victim['victim_sql_text'],
                    victim['victim_sql_hash'],
                    victim['victim_ax_user'],
                    victim['victim_ax_session_id'],

                    # Lock and classification
                    victim['lock_resource_type'],
                    victim['lock_mode'],
                    victim['query_label'],
                    victim['query_label_source'],
                    victim['matched_signature_tokens'],
                    victim['blocker_has_updlock'],
                    victim['victim_has_updlock'],

                    # Metadata
                    CSV_VERSION,
                    SCRIPT_VERSION,
                    CLASSIFIER_VERSION
                ]

                writer.writerow(row_data)

    except (IOError, OSError) as e:
        print(f"Error writing to CSV file {file_path}: {e}")
        raise


def should_send_alert(snapshot: BlockingSnapshot) -> bool:
    """
    Determine if an alert should be sent based on current thresholds.

    Alert conditions (OR logic):
    1. More than X blocked lines globally
    2. Any victim waiting longer than Y seconds
    3. High voice queue backlog (orders > threshold OR picks > threshold)
    """
    # Check blocked lines threshold
    if snapshot.blocked_lines_global > ALERT_BLOCKED_LINES_THRESHOLD:
        return True

    # Check wait time threshold
    if snapshot.max_wait_time_sec_global > ALERT_WAIT_TIME_THRESHOLD:
        return True

    # Check voice queue thresholds
    if snapshot.orders_cnt > ORDERS_THRESHOLD or snapshot.picks_cnt > PICKS_THRESHOLD:
        return True

    return False


def create_alert_email_body(snapshot: BlockingSnapshot) -> str:
    """Create formatted email body for blocking alert."""
    body_lines = [
        f"Orders pending to export: {snapshot.orders_cnt:,}",
        f"Picks pending to export: {snapshot.picks_cnt:,}",
        f"Blocked Lines: {snapshot.blocked_lines_global}",
        f"Max wait time: {format_duration(snapshot.max_wait_time_sec_global)}",
        "",
        "Blocking Details:"
    ]

    if snapshot.victims:
        # Group victims by lead blocker for summary
        blocker_groups = {}
        for victim in snapshot.victims:
            blocker_id = victim['lead_blocker_session_id']
            if blocker_id not in blocker_groups:
                blocker_groups[blocker_id] = {
                    'blocker': victim,
                    'victims': []
                }
            blocker_groups[blocker_id]['victims'].append(victim)

        for blocker_id, group in blocker_groups.items():
            blocker = group['blocker']
            victims = group['victims']

            body_lines.extend([
                f"",
                f"Lead Blocker (Session {blocker_id}):",
                f"  User: {blocker['lead_blocker_ax_user']} on {blocker['lead_blocker_host']}",
                f"  Query: {blocker['query_label']} ({blocker['lead_blocker_command']})",
                f"  Victims: {len(victims)} sessions blocked"
            ])

            # Show top 3 victims by wait time
            sorted_victims = sorted(victims, key=lambda v: v['victim_wait_time_sec'], reverse=True)
            for i, victim in enumerate(sorted_victims[:3]):
                body_lines.append(
                    f"    {i+1}. {victim['victim_ax_user']} on {victim['victim_host']} "
                    f"waiting {format_duration(victim['victim_wait_time_sec'])} ({victim['victim_wait_type']})"
                )
    else:
        body_lines.append("No detailed blocking information available.")

    body_lines.extend([
        "",
        f"Full details logged to {CSV_FILE_PATH}",
        f"Snapshot ID: {snapshot.snapshot_id}"
    ])

    return "\n".join(body_lines)


def run_monitoring_cycle() -> None:
    """
    Execute one complete monitoring cycle.

    1. Connect to replica for voice queue counts
    2. Connect to primary for blocking analysis
    3. Capture comprehensive blocking snapshot
    4. Write to CSV if any blocking detected
    5. Send alert if thresholds exceeded
    """
    connection_replica = None
    connection_primary = None
    classifier = QueryClassifier()

    try:
        # Connect to replica for voice queue counts
        replica_server = SQL_REPLICA
        try:
            connection_replica = pyodbc.connect(
                f'DRIVER={{ODBC Driver 18 for SQL Server}};'
                f'SERVER={SQL_REPLICA};'
                f'DATABASE={DATABASE_NAME};'
                f'Trusted_Connection=yes;'
                f'TrustServerCertificate=yes;'
            )
        except pyodbc.Error as e:
            print(f"Error connecting to replica {SQL_REPLICA}: {e}")
            print(f"Will use primary {SQL_PRIMARY} for voice counts")
            replica_server = SQL_PRIMARY
            connection_replica = None

        # Connect to primary for blocking analysis
        try:
            connection_primary = pyodbc.connect(
                f'DRIVER={{ODBC Driver 18 for SQL Server}};'
                f'SERVER={SQL_PRIMARY};'
                f'DATABASE={DATABASE_NAME};'
                f'Trusted_Connection=yes;'
                f'TrustServerCertificate=yes;'
            )
        except pyodbc.Error as e:
            print(f"Error connecting to primary {SQL_PRIMARY}: {e}")
            print("Cannot proceed without primary connection")
            return

        # Use primary for replica queries if replica connection failed
        if connection_replica is None:
            connection_replica = connection_primary

        # Get voice queue counts
        try:
            orders_cnt, picks_cnt, local_time = get_voice_queue_counts(connection_replica)
        except Exception as e:
            print(f"Error getting voice queue counts: {e}")
            orders_cnt, picks_cnt, local_time = 0, 0, datetime.now()

        # Get comprehensive blocking snapshot
        try:
            snapshot = get_comprehensive_blocking_snapshot(connection_primary, classifier)
            snapshot.orders_cnt = orders_cnt
            snapshot.picks_cnt = picks_cnt
            snapshot.timestamp_local = local_time
            snapshot.server_replica = replica_server
        except Exception as e:
            print(f"Error capturing blocking snapshot: {e}")
            return

        # Write to CSV if there's any blocking activity
        if snapshot.victims:
            try:
                write_snapshot_to_csv(snapshot, CSV_FILE_PATH)
                print(f"Logged {len(snapshot.victims)} blocked victims to {CSV_FILE_PATH}")
            except Exception as e:
                print(f"Error writing to CSV: {e}")

        # Send alert if thresholds exceeded
        if should_send_alert(snapshot):
            try:
                subject = "Alert: Blocking Conditions Exceeded"
                body = create_alert_email_body(snapshot)
                send_email_alert(subject, body)
                print(f"Alert sent: {snapshot.blocked_lines_global} blocked lines, "
                      f"max wait {snapshot.max_wait_time_sec_global}s")
            except Exception as e:
                print(f"Error sending alert email: {e}")
        else:
            # Log status even when no alert
            if snapshot.victims:
                print(f"Blocking detected but below alert thresholds: "
                      f"{snapshot.blocked_lines_global} lines, "
                      f"max wait {snapshot.max_wait_time_sec_global}s")
            else:
                print(f"No blocking detected. Orders: {orders_cnt}, Picks: {picks_cnt}")

    except Exception as e:
        print(f"Unexpected error in monitoring cycle: {e}")

    finally:
        # Clean up connections
        try:
            if connection_replica and connection_replica != connection_primary:
                connection_replica.close()
        except Exception as e:
            print(f"Error closing replica connection: {e}")

        try:
            if connection_primary:
                connection_primary.close()
        except Exception as e:
            print(f"Error closing primary connection: {e}")


def main() -> None:
    """Main monitoring loop."""
    print(f"Starting Voice Monitor v{SCRIPT_VERSION}...")
    print(f"Monitoring interval: {CHECK_INTERVAL_SECONDS} seconds")
    print(f"Alert thresholds: {ALERT_BLOCKED_LINES_THRESHOLD} blocked lines, "
          f"{ALERT_WAIT_TIME_THRESHOLD}s wait time")
    print(f"Voice thresholds: {ORDERS_THRESHOLD} orders, {PICKS_THRESHOLD} picks")
    print(f"Output file: {CSV_FILE_PATH}")
    print(f"Servers: Primary={SQL_PRIMARY}, Replica={SQL_REPLICA}")
    print()

    try:
        while True:
            try:
                run_monitoring_cycle()
            except KeyboardInterrupt:
                print("Monitoring stopped by user")
                break
            except Exception as e:
                print(f"Error in monitoring cycle: {e}")
                print("Continuing to next cycle...")

            try:
                time.sleep(CHECK_INTERVAL_SECONDS)
            except KeyboardInterrupt:
                print("Monitoring stopped by user")
                break

    except Exception as e:
        print(f"Fatal error in main loop: {e}")


if __name__ == '__main__':
    main()
